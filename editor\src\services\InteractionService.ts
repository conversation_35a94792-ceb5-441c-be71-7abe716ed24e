/**
 * 交互服务
 * 用于与底层引擎的交互系统集成
 */
import { EventEmitter } from 'events';

// 临时类型定义，直到 dl-engine-core 可用
export enum InteractionType {
  CLICK = 'click',
  HOVER = 'hover',
  PROXIMITY = 'proximity',
  GRAB = 'grab',
  TOUCH = 'touch'
}

export enum InteractionEventType {
  START = 'start',
  UPDATE = 'update',
  END = 'end',
  CANCEL = 'cancel'
}

export interface InteractionSystemConfig {
  debug: boolean;
  maxInteractionDistance: number;
  enableFrustumCheck: boolean;
  enableHighlight: boolean;
  enablePrompt: boolean;
  enableSound: boolean;
}

export interface InteractableComponent {
  id: string;
  config: any;
}

export interface InteractionSystem {
  on(event: string, callback: (data: any) => void): void;
  registerInteractable(entity: any, component: InteractableComponent): void;
  unregisterInteractable(entity: any): void;
}

// 交互事件类型
export interface InteractionEvent {
  type: string;
  entityId: string;
  interactionType: InteractionType;
  timestamp: number;
  data?: any;
}

// 交互统计信息
export interface InteractionStats {
  totalInteractions: number;
  interactionsByType: Record<InteractionType, number>;
  averageInteractionTime: number;
  mostInteractedEntity: string | null;
  recentInteractions: InteractionEvent[];
}

// 交互配置
export interface InteractionServiceConfig {
  enableLogging: boolean;
  enableStats: boolean;
  maxRecentInteractions: number;
  debugMode: boolean;
}

/**
 * 交互服务类
 */
export class InteractionService extends EventEmitter {
  private static instance: InteractionService | null = null;
  
  private interactionSystem: InteractionSystem | null = null;
  private config: InteractionServiceConfig;
  private stats: InteractionStats;
  private interactionLog: InteractionEvent[] = [];
  private isInitialized = false;

  constructor(config: Partial<InteractionServiceConfig> = {}) {
    super();
    
    this.config = {
      enableLogging: config.enableLogging ?? true,
      enableStats: config.enableStats ?? true,
      maxRecentInteractions: config.maxRecentInteractions ?? 100,
      debugMode: config.debugMode ?? false
    };

    this.stats = {
      totalInteractions: 0,
      interactionsByType: {} as Record<InteractionType, number>,
      averageInteractionTime: 0,
      mostInteractedEntity: null,
      recentInteractions: []
    };

    this.initializeStats();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(config?: Partial<InteractionServiceConfig>): InteractionService {
    if (!InteractionService.instance) {
      InteractionService.instance = new InteractionService(config);
    }
    return InteractionService.instance;
  }

  /**
   * 初始化交互系统
   */
  public async initialize(world: any): Promise<void> {
    if (this.isInitialized) {
      console.warn('InteractionService is already initialized');
      return;
    }

    try {
      // 临时实现：创建模拟的交互系统
      const systemConfig: InteractionSystemConfig = {
        debug: this.config.debugMode,
        maxInteractionDistance: 10,
        enableFrustumCheck: true,
        enableHighlight: true,
        enablePrompt: true,
        enableSound: true
      };

      // 创建模拟的交互系统
      this.interactionSystem = {
        on: (event: string, callback: (data: any) => void) => {
          // 模拟事件监听
        },
        registerInteractable: (entity: any, component: InteractableComponent) => {
          // 模拟注册可交互对象
        },
        unregisterInteractable: (entity: any) => {
          // 模拟注销可交互对象
        }
      };

      // 设置事件监听
      this.setupEventListeners();

      this.isInitialized = true;
      this.emit('initialized');

      if (this.config.debugMode) {
        console.log('InteractionService initialized successfully (mock mode)');
      }
    } catch (error) {
      console.error('Failed to initialize InteractionService:', error);
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.interactionSystem) return;

    // 监听交互事件
    this.interactionSystem.on('interaction', (data: any) => {
      this.handleInteractionEvent(data);
    });

    // 监听高亮事件
    this.interactionSystem.on('highlight', (data: any) => {
      this.emit('highlight', data);
    });

    // 监听提示事件
    this.interactionSystem.on('prompt', (data: any) => {
      this.emit('prompt', data);
    });
  }

  /**
   * 处理交互事件
   */
  private handleInteractionEvent(data: any): void {
    const event: InteractionEvent = {
      type: 'interaction',
      entityId: data.entityId || data.entity?.id || 'unknown',
      interactionType: data.interactionType || InteractionType.CLICK,
      timestamp: Date.now(),
      data
    };

    // 记录事件
    if (this.config.enableLogging) {
      this.logInteraction(event);
    }

    // 更新统计
    if (this.config.enableStats) {
      this.updateStats(event);
    }

    // 发射事件
    this.emit('interaction', event);

    if (this.config.debugMode) {
      console.log('Interaction event:', event);
    }
  }

  /**
   * 记录交互事件
   */
  private logInteraction(event: InteractionEvent): void {
    this.interactionLog.push(event);
    
    // 限制日志大小
    if (this.interactionLog.length > this.config.maxRecentInteractions * 2) {
      this.interactionLog = this.interactionLog.slice(-this.config.maxRecentInteractions);
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(event: InteractionEvent): void {
    this.stats.totalInteractions++;
    
    // 按类型统计
    if (!this.stats.interactionsByType[event.interactionType]) {
      this.stats.interactionsByType[event.interactionType] = 0;
    }
    this.stats.interactionsByType[event.interactionType]++;

    // 添加到最近交互
    this.stats.recentInteractions.push(event);
    if (this.stats.recentInteractions.length > this.config.maxRecentInteractions) {
      this.stats.recentInteractions.shift();
    }

    // 计算最常交互的实体
    this.updateMostInteractedEntity();
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    // 初始化各种交互类型的计数
    Object.values(InteractionType).forEach(type => {
      this.stats.interactionsByType[type] = 0;
    });
  }

  /**
   * 更新最常交互的实体
   */
  private updateMostInteractedEntity(): void {
    const entityCounts: Record<string, number> = {};
    
    this.stats.recentInteractions.forEach(event => {
      entityCounts[event.entityId] = (entityCounts[event.entityId] || 0) + 1;
    });

    let maxCount = 0;
    let mostInteracted = null;
    
    for (const [entityId, count] of Object.entries(entityCounts)) {
      if (count > maxCount) {
        maxCount = count;
        mostInteracted = entityId;
      }
    }

    this.stats.mostInteractedEntity = mostInteracted;
  }

  /**
   * 创建可交互组件
   */
  public async createInteractableComponent(
    entity: any,
    config: any
  ): Promise<InteractableComponent | null> {
    if (!this.isInitialized || !this.interactionSystem) {
      console.error('InteractionService not initialized');
      return null;
    }

    try {
      // 创建模拟的可交互组件
      const component: InteractableComponent = {
        id: `interactable_${Date.now()}`,
        config
      };

      // 模拟添加组件到实体
      if (entity.components) {
        entity.components.Interactable = config;
      }

      // 注册到交互系统
      this.interactionSystem.registerInteractable(entity, component);

      if (this.config.debugMode) {
        console.log('Created interactable component for entity:', entity.id);
      }

      return component;
    } catch (error) {
      console.error('Failed to create interactable component:', error);
      return null;
    }
  }

  /**
   * 移除可交互组件
   */
  public removeInteractableComponent(entity: any): void {
    if (!this.isInitialized || !this.interactionSystem) {
      console.error('InteractionService not initialized');
      return;
    }

    try {
      // 从交互系统注销
      this.interactionSystem.unregisterInteractable(entity);

      // 从实体移除组件
      if (entity.components && entity.components.Interactable) {
        delete entity.components.Interactable;
      }

      if (this.config.debugMode) {
        console.log('Removed interactable component from entity:', entity.id);
      }
    } catch (error) {
      console.error('Failed to remove interactable component:', error);
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): InteractionStats {
    return { ...this.stats };
  }

  /**
   * 获取交互日志
   */
  public getInteractionLog(): InteractionEvent[] {
    return [...this.interactionLog];
  }

  /**
   * 清除统计信息
   */
  public clearStats(): void {
    this.stats = {
      totalInteractions: 0,
      interactionsByType: {} as Record<InteractionType, number>,
      averageInteractionTime: 0,
      mostInteractedEntity: null,
      recentInteractions: []
    };
    this.initializeStats();
    this.emit('statsCleared');
  }

  /**
   * 清除交互日志
   */
  public clearLog(): void {
    this.interactionLog = [];
    this.emit('logCleared');
  }

  /**
   * 销毁服务
   */
  public destroy(): void {
    if (this.interactionSystem) {
      // 这里应该从世界中移除系统，但需要世界实例
      // world.removeSystem(this.interactionSystem);
      this.interactionSystem = null;
    }

    this.removeAllListeners();
    this.isInitialized = false;
    InteractionService.instance = null;

    if (this.config.debugMode) {
      console.log('InteractionService destroyed');
    }
  }

  /**
   * 获取交互系统实例
   */
  public getInteractionSystem(): InteractionSystem | null {
    return this.interactionSystem;
  }

  /**
   * 检查是否已初始化
   */
  public isReady(): boolean {
    return this.isInitialized && this.interactionSystem !== null;
  }
}

// 导出单例实例
export const interactionService = InteractionService.getInstance();
export default interactionService;
