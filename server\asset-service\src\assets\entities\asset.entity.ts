/**
 * 资产实体
 */
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { AssetVersion } from './asset-version.entity';
import { AssetTag } from './asset-tag.entity';

export enum AssetType {
  MODEL = 'model',
  TEXTURE = 'texture',
  AUDIO = 'audio',
  OTHER = 'other',
}

export enum AssetStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  READY = 'ready',
  ERROR = 'error',
}

@Entity('assets')
export class Asset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: AssetType,
    default: AssetType.OTHER,
  })
  type: AssetType;

  @Column({
    type: 'enum',
    enum: AssetStatus,
    default: AssetStatus.PENDING,
  })
  status: AssetStatus;

  @Column({ nullable: true })
  thumbnailUrl: string;

  @Column()
  ownerId: string;

  @Column({ nullable: true })
  projectId: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @OneToMany(() => AssetVersion, version => version.asset, { cascade: true })
  versions: AssetVersion[];

  @OneToMany(() => AssetTag, tag => tag.asset, { cascade: true })
  tags: AssetTag[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
