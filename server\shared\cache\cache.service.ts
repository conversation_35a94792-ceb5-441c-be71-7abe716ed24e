/**
 * 缓存服务
 * 提供通用的缓存功能，支持多级缓存（内存和Redis）
 */
import { Injectable, Inject, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { CacheConfig, CacheLevel } from './interfaces/cache-config.interface';
import { CacheStats } from './interfaces/cache-stats.interface';
import * as Redis from 'ioredis';

/**
 * 缓存条目
 */
interface CacheEntry<T> {
  /** 数据 */
  data: T;
  /** 过期时间 */
  expireAt: number;
  /** 最后访问时间 */
  lastAccessed: number;
  /** 访问次数 */
  accessCount: number;
}

@Injectable()
export class CacheService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(CacheService.name);
  private readonly memoryCache = new Map<string, CacheEntry<any>>();
  private redisClient: Redis.Redis;
  private cleanupInterval: NodeJS.Timeout;
  private statsInterval: NodeJS.Timeout;

  // 缓存统计
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0,
    avgAccessTime: 0,
    memoryUsage: 0,
    expirations: 0,
    evictions: 0,
    writes: 0,
    deletes: 0,
    clears: 0,
  };

  // 访问时间统计
  private totalAccessTime = 0;
  private accessCount = 0;

  // 默认配置
  private readonly defaultConfig: Required<CacheConfig> = {
    enabled: true,
    levels: [CacheLevel.MEMORY],
    memoryTtl: 60000, // 1分钟
    redisTtl: 300000, // 5分钟
    maxEntries: 10000,
    enableAdaptiveTtl: true,
    enableStats: true,
    debug: false,
    redis: {
      host: 'localhost',
      port: 6379,
      password: '',
      db: 0,
      keyPrefix: 'cache:',
    },
  };

  // 合并后的配置
  private readonly config: Required<CacheConfig>;

  constructor(@Inject('CACHE_OPTIONS') private readonly options: CacheConfig) {
    // 合并配置
    this.config = {
      ...this.defaultConfig,
      ...options,
      redis: {
        ...this.defaultConfig.redis,
        ...options.redis,
      },
    };
  }

  /**
   * 模块初始化
   */
  async onModuleInit() {
    if (!this.config.enabled) {
      this.logger.log('缓存服务已禁用');
      return;
    }

    // 初始化Redis客户端
    if (this.config.levels.includes(CacheLevel.REDIS)) {
      try {
        this.redisClient = new Redis({
          host: this.config.redis.host,
          port: this.config.redis.port,
          password: this.config.redis.password || undefined,
          db: this.config.redis.db,
          keyPrefix: this.config.redis.keyPrefix,
        });

        this.logger.log(`已连接到Redis服务器: ${this.config.redis.host}:${this.config.redis.port}`);
      } catch (error) {
        this.logger.error(`连接Redis服务器失败: ${error.message}`, error.stack);
      }
    }

    // 设置定期清理
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // 每分钟清理一次

    // 设置定期统计
    if (this.config.enableStats) {
      this.statsInterval = setInterval(() => {
        this.updateMemoryUsage();
      }, 30000); // 每30秒更新一次内存使用情况
    }

    this.logger.log(`缓存服务已初始化，启用级别: ${this.config.levels.join(', ')}`);
  }

  /**
   * 模块销毁
   */
  onModuleDestroy() {
    // 清除定时器
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }

    // 关闭Redis连接
    if (this.redisClient) {
      this.redisClient.disconnect();
    }

    this.logger.log('缓存服务已销毁');
  }

  /**
   * 从缓存获取数据
   * @param key 缓存键
   * @param fetchFn 获取数据的函数
   * @param ttl 过期时间（毫秒）
   */
  async get<T>(key: string, fetchFn: () => Promise<T>, ttl?: number): Promise<T> {
    if (!this.config.enabled) {
      return fetchFn();
    }

    const cacheKey = this.getCacheKey(key);
    const startTime = Date.now();

    // 尝试从内存缓存获取
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      const memoryEntry = this.memoryCache.get(cacheKey);

      if (memoryEntry && memoryEntry.expireAt > Date.now()) {
        // 更新访问统计
        memoryEntry.lastAccessed = Date.now();
        memoryEntry.accessCount++;

        // 更新缓存统计
        this.updateStats('hit', startTime);

        return memoryEntry.data;
      }
    }

    // 尝试从Redis缓存获取
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        const redisData = await this.redisClient.get(redisKey);

        if (redisData) {
          const data = JSON.parse(redisData);

          // 更新内存缓存
          if (this.config.levels.includes(CacheLevel.MEMORY)) {
            this.setMemoryCache(cacheKey, data, ttl || this.config.memoryTtl);
          }

          // 更新缓存统计
          this.updateStats('hit', startTime);

          return data;
        }
      } catch (error) {
        this.logger.warn(`从Redis获取缓存失败: ${error.message}`);
      }
    }

    // 缓存未命中，获取新数据
    this.updateStats('miss', startTime);

    try {
      const data = await fetchFn();

      // 更新缓存
      this.set(key, data, ttl);

      return data;
    } catch (error) {
      this.logger.error(`获取数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 数据
   * @param ttl 过期时间（毫秒）
   */
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const cacheKey = this.getCacheKey(key);

    // 设置内存缓存
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      this.setMemoryCache(cacheKey, data, ttl || this.config.memoryTtl);
    }

    // 设置Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        const redisTtl = ttl || this.config.redisTtl;

        await this.redisClient.set(
          redisKey,
          JSON.stringify(data),
          'PX',
          redisTtl,
        );
      } catch (error) {
        this.logger.warn(`设置Redis缓存失败: ${error.message}`);
      }
    }

    // 更新统计
    this.stats.writes++;
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  async delete(key: string): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const cacheKey = this.getCacheKey(key);

    // 删除内存缓存
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      this.memoryCache.delete(cacheKey);
    }

    // 删除Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        await this.redisClient.del(redisKey);
      } catch (error) {
        this.logger.warn(`删除Redis缓存失败: ${error.message}`);
      }
    }

    // 更新统计
    this.stats.deletes++;
    this.stats.size = this.memoryCache.size;
  }

  /**
   * 清除所有缓存
   */
  async clear(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    // 清除内存缓存
    this.memoryCache.clear();

    // 清除Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const pattern = this.getRedisKey('*');
        const keys = await this.redisClient.keys(pattern);

        if (keys.length > 0) {
          await this.redisClient.del(...keys);
        }
      } catch (error) {
        this.logger.warn(`清除Redis缓存失败: ${error.message}`);
      }
    }

    // 更新统计
    this.stats.clears++;
    this.stats.size = 0;
    this.logger.log('已清除所有缓存');
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置缓存统计信息
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      size: this.memoryCache.size,
      hitRate: 0,
      avgAccessTime: 0,
      memoryUsage: 0,
      expirations: 0,
      evictions: 0,
      writes: 0,
      deletes: 0,
      clears: 0,
    };
    this.totalAccessTime = 0;
    this.accessCount = 0;
  }

  /**
   * 设置内存缓存
   * @param key 缓存键
   * @param data 数据
   * @param ttl 过期时间（毫秒）
   */
  private setMemoryCache<T>(key: string, data: T, ttl: number): void {
    // 检查缓存大小
    if (this.memoryCache.size >= this.config.maxEntries) {
      this.evictCache();
    }

    // 设置缓存
    this.memoryCache.set(key, {
      data,
      expireAt: Date.now() + ttl,
      lastAccessed: Date.now(),
      accessCount: 0,
    });

    // 更新统计
    this.stats.size = this.memoryCache.size;
  }

  /**
   * 驱逐缓存
   */
  private evictCache(): void {
    // 如果缓存为空，直接返回
    if (this.memoryCache.size === 0) return;

    // 计算要驱逐的项数
    const evictCount = Math.max(1, Math.floor(this.memoryCache.size * 0.1));

    // 按最后访问时间和访问次数排序
    const sortedItems = Array.from(this.memoryCache.entries())
      .sort(([, a], [, b]) => {
        // 首先按访问次数排序
        const countDiff = a.accessCount - b.accessCount;
        if (countDiff !== 0) return countDiff;

        // 然后按最后访问时间排序
        return a.lastAccessed - b.lastAccessed;
      });

    // 驱逐最不常用的项
    for (let i = 0; i < evictCount && i < sortedItems.length; i++) {
      this.memoryCache.delete(sortedItems[i][0]);
      this.stats.evictions++;
    }

    // 如果启用调试，输出日志
    if (this.config.debug) {
      this.logger.debug(`驱逐了 ${evictCount} 个缓存项`);
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;

    // 清理内存缓存
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.expireAt <= now) {
        this.memoryCache.delete(key);
        expiredCount++;
        this.stats.expirations++;
      }
    }

    // 更新统计
    this.stats.size = this.memoryCache.size;

    // 如果启用调试，输出日志
    if (this.config.debug && expiredCount > 0) {
      this.logger.debug(`清理了 ${expiredCount} 个过期缓存项`);
    }
  }

  /**
   * 更新缓存统计
   * @param type 操作类型
   * @param startTime 开始时间
   */
  private updateStats(type: 'hit' | 'miss', startTime: number): void {
    if (!this.config.enableStats) return;

    const duration = Date.now() - startTime;

    if (type === 'hit') {
      this.stats.hits++;
    } else {
      this.stats.misses++;
    }

    // 更新访问时间统计
    this.totalAccessTime += duration;
    this.accessCount++;
    this.stats.avgAccessTime = this.totalAccessTime / this.accessCount;

    // 更新命中率
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0;
  }

  /**
   * 更新内存使用情况
   */
  private updateMemoryUsage(): void {
    if (!this.config.enableStats) return;

    // 估算内存使用量
    let totalSize = 0;
    for (const [key, entry] of this.memoryCache.entries()) {
      // 估算键的大小
      totalSize += key.length * 2; // 假设每个字符占用2字节

      // 估算值的大小
      try {
        const jsonSize = JSON.stringify(entry.data).length * 2; // 假设每个字符占用2字节
        totalSize += jsonSize;
      } catch (e) {
        // 忽略无法序列化的对象
      }

      // 估算元数据的大小
      totalSize += 24; // 假设元数据占用24字节
    }

    this.stats.memoryUsage = totalSize;
  }

  /**
   * 获取缓存键
   * @param key 原始键
   * @returns 缓存键
   */
  private getCacheKey(key: string): string {
    return `cache:${key}`;
  }

  /**
   * 获取Redis键
   * @param key 原始键
   * @returns Redis键
   */
  private getRedisKey(key: string): string {
    return key;
  }
}
