/**
 * 事件总线接口定义
 */

/**
 * 事件接口
 */
export interface Event {
  /**
   * 事件名称
   */
  name: string;
  
  /**
   * 事件数据
   */
  data: any;
  
  /**
   * 事件发布时间
   */
  timestamp: number;
  
  /**
   * 事件发布者
   */
  publisher: string;
  
  /**
   * 事件ID
   */
  id: string;
  
  /**
   * 事件版本
   */
  version: number;
  
  /**
   * 事件相关ID
   */
  correlationId?: string;
  
  /**
   * 事件因果ID
   */
  causationId?: string;
}

/**
 * 事件处理器接口
 */
export interface EventHandler<T extends Event = Event> {
  /**
   * 处理事件
   * @param event 事件对象
   */
  handle(event: T): Promise<void>;
}

/**
 * 事件总线配置
 */
export interface EventBusOptions {
  /**
   * Redis配置
   */
  redis?: {
    host: string;
    port: number;
    password?: string;
    db?: number;
  };
  
  /**
   * 服务名称
   */
  serviceName: string;
  
  /**
   * 事件通道前缀
   */
  channelPrefix?: string;
  
  /**
   * 是否启用持久化
   */
  enablePersistence?: boolean;
  
  /**
   * 是否启用本地事件处理
   */
  enableLocalEvents?: boolean;
}
