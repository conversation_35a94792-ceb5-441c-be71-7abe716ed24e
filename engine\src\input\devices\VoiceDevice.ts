/**
 * 语音输入设备
 * 用于处理语音命令和语音识别
 */
import { BaseInputDevice } from '../InputDevice';

/**
 * 语音识别状态
 */
export enum VoiceRecognitionState {
  /** 未初始化 */
  UNINITIALIZED = 'uninitialized',
  /** 初始化中 */
  INITIALIZING = 'initializing',
  /** 就绪 */
  READY = 'ready',
  /** 录音中 */
  RECORDING = 'recording',
  /** 识别中 */
  RECOGNIZING = 'recognizing',
  /** 错误 */
  ERROR = 'error'
}

/**
 * 语音识别结果
 */
export interface VoiceRecognitionResult {
  /** 识别文本 */
  text: string;
  /** 置信度 */
  confidence: number;
  /** 是否为最终结果 */
  isFinal: boolean;
  /** 替代结果 */
  alternatives?: Array<{
    /** 识别文本 */
    text: string;
    /** 置信度 */
    confidence: number;
  }>;
}

/**
 * 语音命令
 */
export interface VoiceCommand {
  /** 命令名称 */
  name: string;
  /** 命令关键词 */
  keywords: string[];
  /** 命令回调函数 */
  callback: (result: VoiceRecognitionResult) => void;
  /** 置信度阈值 */
  confidenceThreshold?: number;
}

/**
 * 语音输入设备选项
 */
export interface VoiceDeviceOptions {
  /** 是否自动开始 */
  autoStart?: boolean;
  /** 语言 */
  language?: string;
  /** 是否连续识别 */
  continuous?: boolean;
  /** 是否返回中间结果 */
  interimResults?: boolean;
  /** 最大替代结果数 */
  maxAlternatives?: number;
  /** 置信度阈值 */
  confidenceThreshold?: number;
  /** 语音命令列表 */
  commands?: VoiceCommand[];
}

/**
 * 语音输入设备
 */
export class VoiceDevice extends BaseInputDevice {
  /** 语音识别器 */
  private recognition: SpeechRecognition | null = null;

  /** 语音合成器 */
  private synthesis: SpeechSynthesis | null = null;

  /** 语音识别状态 */
  private state: VoiceRecognitionState = VoiceRecognitionState.UNINITIALIZED;

  /** 是否自动开始 */
  private autoStart: boolean;

  /** 语言 */
  private language: string;

  /** 是否连续识别 */
  private continuous: boolean;

  /** 是否返回中间结果 */
  private interimResults: boolean;

  /** 最大替代结果数 */
  private maxAlternatives: number;

  /** 置信度阈值 */
  private confidenceThreshold: number;

  /** 语音命令列表 */
  private commands: VoiceCommand[] = [];

  /** 当前识别结果 */
  private currentResult: VoiceRecognitionResult | null = null;

  /** 是否支持语音识别 */
  private isRecognitionSupported: boolean = false;

  /** 是否支持语音合成 */
  private isSynthesisSupported: boolean = false;

  /**
   * 创建语音输入设备
   * @param options 选项
   */
  constructor(options: VoiceDeviceOptions = {}) {
    super('voice');

    this.autoStart = options.autoStart !== undefined ? options.autoStart : false;
    this.language = options.language || 'zh-CN';
    this.continuous = options.continuous !== undefined ? options.continuous : true;
    this.interimResults = options.interimResults !== undefined ? options.interimResults : true;
    this.maxAlternatives = options.maxAlternatives || 1;
    this.confidenceThreshold = options.confidenceThreshold || 0.5;

    // 添加语音命令
    if (options.commands) {
      this.addCommands(options.commands);
    }

    // 检查浏览器支持
    this.checkBrowserSupport();
  }

  /**
   * 检查浏览器支持
   */
  private checkBrowserSupport(): void {
    // 检查语音识别支持
    this.isRecognitionSupported = 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;

    // 检查语音合成支持
    this.isSynthesisSupported = 'speechSynthesis' in window;

    // 更新设备值
    this.setValue('isRecognitionSupported', this.isRecognitionSupported);
    this.setValue('isSynthesisSupported', this.isSynthesisSupported);
  }

  /**
   * 初始化设备
   */
  public initialize(): void {
    if (this.initialized) return;

    // 初始化语音识别
    this.initSpeechRecognition();

    // 初始化语音合成
    this.initSpeechSynthesis();

    // 如果自动开始，则开始识别
    if (this.autoStart && this.isRecognitionSupported) {
      this.startRecognition();
    }

    super.initialize();
  }

  /**
   * 初始化语音识别
   */
  private initSpeechRecognition(): void {
    if (!this.isRecognitionSupported) {
      this.state = VoiceRecognitionState.ERROR;
      this.setValue('state', this.state);
      this.eventEmitter.emit('error', { message: '浏览器不支持语音识别' });
      return;
    }

    try {
      // 创建语音识别器
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();

      // 设置选项
      this.recognition.lang = this.language;
      this.recognition.continuous = this.continuous;
      this.recognition.interimResults = this.interimResults;
      this.recognition.maxAlternatives = this.maxAlternatives;

      // 添加事件监听器
      this.recognition.onstart = this.handleRecognitionStart.bind(this);
      this.recognition.onresult = this.handleRecognitionResult.bind(this);
      this.recognition.onerror = this.handleRecognitionError.bind(this);
      this.recognition.onend = this.handleRecognitionEnd.bind(this);

      // 更新状态
      this.state = VoiceRecognitionState.READY;
      this.setValue('state', this.state);
      this.eventEmitter.emit('ready');
    } catch (error) {
      this.state = VoiceRecognitionState.ERROR;
      this.setValue('state', this.state);
      this.eventEmitter.emit('error', { message: '初始化语音识别失败', error });
    }
  }

  /**
   * 初始化语音合成
   */
  private initSpeechSynthesis(): void {
    if (!this.isSynthesisSupported) {
      this.eventEmitter.emit('error', { message: '浏览器不支持语音合成' });
      return;
    }

    try {
      // 获取语音合成器
      this.synthesis = window.speechSynthesis;
    } catch (error) {
      this.eventEmitter.emit('error', { message: '初始化语音合成失败', error });
    }
  }

  /**
   * 处理识别开始事件
   */
  private handleRecognitionStart(): void {
    this.state = VoiceRecognitionState.RECORDING;
    this.setValue('state', this.state);
    this.eventEmitter.emit('recognitionStart');
  }

  /**
   * 处理识别结果事件
   * @param event 识别结果事件
   */
  private handleRecognitionResult(event: SpeechRecognitionEvent): void {
    this.state = VoiceRecognitionState.RECOGNIZING;
    this.setValue('state', this.state);

    // 获取最新的识别结果
    const result = event.results[event.results.length - 1];
    const isFinal = result.isFinal;
    const text = result[0].transcript.trim();
    const confidence = result[0].confidence;

    // 创建识别结果对象
    const recognitionResult: VoiceRecognitionResult = {
      text,
      confidence,
      isFinal,
      alternatives: []
    };

    // 添加替代结果
    for (let i = 1; i < result.length; i++) {
      recognitionResult.alternatives!.push({
        text: result[i].transcript.trim(),
        confidence: result[i].confidence
      });
    }

    // 更新当前结果
    this.currentResult = recognitionResult;
    this.setValue('result', recognitionResult);

    // 触发结果事件
    this.eventEmitter.emit('result', recognitionResult);

    // 如果是最终结果，则处理命令
    if (isFinal && confidence >= this.confidenceThreshold) {
      this.processCommands(recognitionResult);
    }
  }

  /**
   * 处理识别错误事件
   * @param event 错误事件
   */
  private handleRecognitionError(event: SpeechRecognitionErrorEvent): void {
    this.state = VoiceRecognitionState.ERROR;
    this.setValue('state', this.state);
    this.eventEmitter.emit('error', { message: '语音识别错误', error: event.error });
  }

  /**
   * 处理识别结束事件
   */
  private handleRecognitionEnd(): void {
    // 如果是连续模式，则自动重新开始
    if (this.continuous && this.state !== VoiceRecognitionState.ERROR) {
      this.startRecognition();
    } else {
      this.state = VoiceRecognitionState.READY;
      this.setValue('state', this.state);
      this.eventEmitter.emit('recognitionEnd');
    }
  }

  /**
   * 处理语音命令
   * @param result 识别结果
   */
  private processCommands(result: VoiceRecognitionResult): void {
    const text = result.text.toLowerCase();

    // 检查每个命令
    for (const command of this.commands) {
      // 检查命令关键词
      for (const keyword of command.keywords) {
        if (text.includes(keyword.toLowerCase())) {
          // 检查置信度
          const threshold = command.confidenceThreshold || this.confidenceThreshold;
          if (result.confidence >= threshold) {
            // 执行命令回调
            command.callback(result);
            // 触发命令事件
            this.eventEmitter.emit('command', { command: command.name, result });
            return;
          }
        }
      }
    }
  }

  /**
   * 开始语音识别
   */
  public startRecognition(): void {
    if (!this.isRecognitionSupported || !this.recognition) {
      this.eventEmitter.emit('error', { message: '语音识别不可用' });
      return;
    }

    if (this.state === VoiceRecognitionState.RECORDING || this.state === VoiceRecognitionState.RECOGNIZING) {
      return;
    }

    try {
      this.recognition.start();
    } catch (error) {
      this.eventEmitter.emit('error', { message: '启动语音识别失败', error });
    }
  }

  /**
   * 停止语音识别
   */
  public stopRecognition(): void {
    if (!this.isRecognitionSupported || !this.recognition) {
      return;
    }

    if (this.state !== VoiceRecognitionState.RECORDING && this.state !== VoiceRecognitionState.RECOGNIZING) {
      return;
    }

    try {
      this.recognition.stop();
    } catch (error) {
      this.eventEmitter.emit('error', { message: '停止语音识别失败', error });
    }
  }

  /**
   * 添加语音命令
   * @param commands 语音命令列表
   */
  public addCommands(commands: VoiceCommand[]): void {
    this.commands.push(...commands);
  }

  /**
   * 移除语音命令
   * @param name 命令名称
   */
  public removeCommand(name: string): void {
    this.commands = this.commands.filter(command => command.name !== name);
  }

  /**
   * 清除所有语音命令
   */
  public clearCommands(): void {
    this.commands = [];
  }

  /**
   * 获取当前识别结果
   * @returns 识别结果
   */
  public getResult(): VoiceRecognitionResult | null {
    return this.currentResult;
  }

  /**
   * 获取当前识别状态
   * @returns 识别状态
   */
  public getState(): VoiceRecognitionState {
    return this.state;
  }

  /**
   * 语音合成
   * @param text 文本
   * @param options 选项
   */
  public speak(text: string, options: SpeechSynthesisUtteranceOptions = {}): void {
    if (!this.isSynthesisSupported || !this.synthesis) {
      this.eventEmitter.emit('error', { message: '语音合成不可用' });
      return;
    }

    // 创建语音合成话语
    const utterance = new SpeechSynthesisUtterance(text);

    // 设置选项
    if (options.voice) utterance.voice = options.voice;
    if (options.volume !== undefined) utterance.volume = options.volume;
    if (options.rate !== undefined) utterance.rate = options.rate;
    if (options.pitch !== undefined) utterance.pitch = options.pitch;
    if (options.lang) utterance.lang = options.lang;

    // 添加事件监听器
    utterance.onstart = () => this.eventEmitter.emit('speakStart', { text });
    utterance.onend = () => this.eventEmitter.emit('speakEnd', { text });
    utterance.onerror = (event) => this.eventEmitter.emit('error', { message: '语音合成错误', error: event });

    // 开始合成
    this.synthesis.speak(utterance);
  }

  /**
   * 销毁设备
   */
  public destroy(): void {
    if (this.destroyed) return;

    // 停止语音识别
    this.stopRecognition();

    // 停止语音合成
    if (this.isSynthesisSupported && this.synthesis) {
      this.synthesis.cancel();
    }

    super.destroy();
  }
}
