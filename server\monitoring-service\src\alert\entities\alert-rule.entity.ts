import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { AlertSeverity } from './alert.entity';

export enum AlertRuleType {
  THRESHOLD = 'threshold',
  ANOMALY = 'anomaly',
  ABSENCE = 'absence',
  RATE_OF_CHANGE = 'rate_of_change',
  COMPOSITE = 'composite',
}

export enum AlertRuleStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
}

export enum AlertRuleOperator {
  GT = '>',
  GTE = '>=',
  LT = '<',
  LTE = '<=',
  EQ = '==',
  NEQ = '!=',
}

@Entity('alert_rules')
export class AlertRuleEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  name: string;

  @Column('text')
  description: string;

  @Column({
    type: 'enum',
    enum: AlertRuleType,
    default: AlertRuleType.THRESHOLD,
  })
  type: AlertRuleType;

  @Column({
    type: 'enum',
    enum: AlertRuleStatus,
    default: AlertRuleStatus.ACTIVE,
  })
  @Index()
  status: AlertRuleStatus;

  @Column({
    type: 'enum',
    enum: AlertSeverity,
    default: AlertSeverity.WARNING,
  })
  severity: AlertSeverity;

  @Column()
  @Index()
  metricName: string;

  @Column({ nullable: true })
  @Index()
  serviceType: string;

  @Column('json', { nullable: true })
  labels: Record<string, string>;

  @Column({
    type: 'enum',
    enum: AlertRuleOperator,
    default: AlertRuleOperator.GT,
  })
  operator: AlertRuleOperator;

  @Column('float')
  threshold: number;

  @Column('int', { default: 1 })
  forDuration: number;

  @Column('int', { default: 0 })
  evaluationInterval: number;

  @Column('json', { nullable: true })
  additionalConditions: Record<string, any>;

  @Column('json')
  annotations: Record<string, string>;

  @Column('simple-array', { default: '' })
  notificationChannels: string[];

  @Column('boolean', { default: true })
  enabled: boolean;

  @Column('int', { default: 0 })
  cooldown: number;

  @Column('timestamp', { nullable: true })
  lastTriggered: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
