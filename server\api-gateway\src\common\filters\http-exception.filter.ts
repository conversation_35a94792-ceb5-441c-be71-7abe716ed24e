/**
 * HTTP异常过滤器
 */
import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const errorResponse = exception.getResponse();

    const errorMessage = 
      typeof errorResponse === 'string'
        ? errorResponse
        : typeof errorResponse === 'object' && 'message' in errorResponse
        ? Array.isArray(errorResponse.message)
          ? errorResponse.message[0]
          : errorResponse.message
        : '未知错误';

    const error = 
      typeof errorResponse === 'object' && 'error' in errorResponse
        ? errorResponse.error
        : exception.name;

    this.logger.error(
      `${request.method} ${request.url} ${status} - ${errorMessage}`,
      exception.stack,
    );

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      error,
      message: errorMessage,
    });
  }
}
