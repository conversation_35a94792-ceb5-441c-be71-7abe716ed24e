/**
 * 资产服务
 */
import { Injectable, NotFoundException, ForbiddenException, BadRequestException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { Asset, AssetType, AssetStatus } from './entities/asset.entity';
import { AssetVersion } from './entities/asset-version.entity';
import { AssetTag } from './entities/asset-tag.entity';
import { CreateAssetDto } from './dto/create-asset.dto';
import { UpdateAssetDto } from './dto/update-asset.dto';

@Injectable()
export class AssetsService {
  constructor(
    @InjectRepository(Asset)
    private readonly assetRepository: Repository<Asset>,
    @InjectRepository(AssetVersion)
    private readonly assetVersionRepository: Repository<AssetVersion>,
    @InjectRepository(AssetTag)
    private readonly assetTagRepository: Repository<AssetTag>,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  /**
   * 创建资产
   */
  async create(userId: string, createAssetDto: CreateAssetDto, file?: Express.Multer.File): Promise<Asset> {
    // 验证用户是否存在
    try {
      await firstValueFrom(this.userService.send({ cmd: 'findUserById' }, userId));
    } catch (error) {
      throw new BadRequestException('用户不存在');
    }

    // 如果指定了项目ID，验证项目是否存在并检查权限
    if (createAssetDto.projectId) {
      try {
        await firstValueFrom(
          this.projectService.send(
            { cmd: 'checkProjectPermission' }, 
            { 
              projectId: createAssetDto.projectId, 
              userId, 
              roles: ['owner', 'admin', 'editor'] 
            }
          )
        );
      } catch (error) {
        throw new ForbiddenException('您没有权限在此项目中创建资产');
      }
    }

    // 确定资产类型
    let assetType = createAssetDto.type || AssetType.OTHER;
    if (file) {
      if (file.mimetype.startsWith('image/')) {
        assetType = AssetType.TEXTURE;
      } else if (file.mimetype.startsWith('audio/')) {
        assetType = AssetType.AUDIO;
      } else if (file.mimetype.startsWith('model/') || 
                file.originalname.endsWith('.gltf') || 
                file.originalname.endsWith('.glb') || 
                file.originalname.endsWith('.obj') || 
                file.originalname.endsWith('.fbx')) {
        assetType = AssetType.MODEL;
      }
    }

    // 创建资产
    const asset = this.assetRepository.create({
      ...createAssetDto,
      type: assetType,
      ownerId: userId,
      status: file ? AssetStatus.PROCESSING : AssetStatus.PENDING,
    });

    // 保存资产
    const savedAsset = await this.assetRepository.save(asset);

    // 添加标签
    if (createAssetDto.tags && createAssetDto.tags.length > 0) {
      for (const tagName of createAssetDto.tags) {
        const tag = this.assetTagRepository.create({
          name: tagName,
          assetId: savedAsset.id,
        });
        await this.assetTagRepository.save(tag);
      }
    }

    // 如果上传了文件，创建资产版本
    if (file) {
      // 计算文件哈希
      const fileBuffer = fs.readFileSync(file.path);
      const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

      // 创建资产版本
      const assetVersion = this.assetVersionRepository.create({
        assetId: savedAsset.id,
        version: 1,
        fileUrl: file.path.replace(/\\/g, '/'),
        fileSize: file.size,
        mimeType: file.mimetype,
        hash,
        status: AssetStatus.PROCESSING,
        metadata: {
          originalName: file.originalname,
        },
      });
      await this.assetVersionRepository.save(assetVersion);

      // 异步处理资产
      this.processAsset(savedAsset.id, assetVersion.id);
    }

    return this.findOne(savedAsset.id, userId);
  }

  /**
   * 查找所有资产
   */
  async findAll(userId: string, projectId?: string, type?: AssetType, tags?: string[]): Promise<Asset[]> {
    const queryBuilder = this.assetRepository.createQueryBuilder('asset')
      .leftJoinAndSelect('asset.versions', 'version')
      .leftJoinAndSelect('asset.tags', 'tag');

    // 过滤条件
    if (projectId) {
      queryBuilder.andWhere('asset.projectId = :projectId', { projectId });
    } else {
      // 如果没有指定项目，只返回用户自己的资产和公共资产
      queryBuilder.andWhere('asset.ownerId = :userId OR asset.projectId IS NULL', { userId });
    }

    if (type) {
      queryBuilder.andWhere('asset.type = :type', { type });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere('tag.name IN (:...tags)', { tags });
    }

    // 排序
    queryBuilder.orderBy('asset.createdAt', 'DESC');

    return queryBuilder.getMany();
  }

  /**
   * 查找单个资产
   */
  async findOne(id: string, userId: string): Promise<Asset> {
    const asset = await this.assetRepository.findOne({
      where: { id },
      relations: ['versions', 'tags'],
    });

    if (!asset) {
      throw new NotFoundException(`资产ID ${id} 不存在`);
    }

    // 检查权限
    if (asset.ownerId !== userId && asset.projectId) {
      try {
        await firstValueFrom(
          this.projectService.send(
            { cmd: 'checkProjectPermission' }, 
            { 
              projectId: asset.projectId, 
              userId, 
              roles: ['owner', 'admin', 'editor', 'viewer'] 
            }
          )
        );
      } catch (error) {
        throw new ForbiddenException('您没有权限访问此资产');
      }
    }

    return asset;
  }

  /**
   * 更新资产
   */
  async update(id: string, userId: string, updateAssetDto: UpdateAssetDto): Promise<Asset> {
    const asset = await this.findOne(id, userId);

    // 检查权限
    if (asset.ownerId !== userId && asset.projectId) {
      try {
        await firstValueFrom(
          this.projectService.send(
            { cmd: 'checkProjectPermission' }, 
            { 
              projectId: asset.projectId, 
              userId, 
              roles: ['owner', 'admin', 'editor'] 
            }
          )
        );
      } catch (error) {
        throw new ForbiddenException('您没有权限更新此资产');
      }
    }

    // 更新资产
    Object.assign(asset, updateAssetDto);

    // 更新标签
    if (updateAssetDto.tags) {
      // 删除旧标签
      await this.assetTagRepository.delete({ assetId: asset.id });

      // 添加新标签
      for (const tagName of updateAssetDto.tags) {
        const tag = this.assetTagRepository.create({
          name: tagName,
          assetId: asset.id,
        });
        await this.assetTagRepository.save(tag);
      }
    }

    return this.assetRepository.save(asset);
  }

  /**
   * 删除资产
   */
  async remove(id: string, userId: string): Promise<void> {
    const asset = await this.findOne(id, userId);

    // 检查权限
    if (asset.ownerId !== userId && asset.projectId) {
      try {
        await firstValueFrom(
          this.projectService.send(
            { cmd: 'checkProjectPermission' }, 
            { 
              projectId: asset.projectId, 
              userId, 
              roles: ['owner', 'admin'] 
            }
          )
        );
      } catch (error) {
        throw new ForbiddenException('您没有权限删除此资产');
      }
    }

    // 删除文件
    for (const version of asset.versions) {
      try {
        fs.unlinkSync(version.fileUrl);
      } catch (error) {
        // 忽略文件删除错误
      }
    }

    await this.assetRepository.remove(asset);
  }

  /**
   * 上传新版本
   */
  async uploadVersion(id: string, userId: string, file: Express.Multer.File): Promise<AssetVersion> {
    const asset = await this.findOne(id, userId);

    // 检查权限
    if (asset.ownerId !== userId && asset.projectId) {
      try {
        await firstValueFrom(
          this.projectService.send(
            { cmd: 'checkProjectPermission' }, 
            { 
              projectId: asset.projectId, 
              userId, 
              roles: ['owner', 'admin', 'editor'] 
            }
          )
        );
      } catch (error) {
        throw new ForbiddenException('您没有权限更新此资产');
      }
    }

    // 计算文件哈希
    const fileBuffer = fs.readFileSync(file.path);
    const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

    // 获取最新版本号
    const latestVersion = await this.assetVersionRepository.findOne({
      where: { assetId: asset.id },
      order: { version: 'DESC' },
    });

    const newVersion = latestVersion ? latestVersion.version + 1 : 1;

    // 创建资产版本
    const assetVersion = this.assetVersionRepository.create({
      assetId: asset.id,
      version: newVersion,
      fileUrl: file.path.replace(/\\/g, '/'),
      fileSize: file.size,
      mimeType: file.mimetype,
      hash,
      status: AssetStatus.PROCESSING,
      metadata: {
        originalName: file.originalname,
      },
    });

    const savedVersion = await this.assetVersionRepository.save(assetVersion);

    // 更新资产状态
    asset.status = AssetStatus.PROCESSING;
    await this.assetRepository.save(asset);

    // 异步处理资产
    this.processAsset(asset.id, savedVersion.id);

    return savedVersion;
  }

  /**
   * 处理资产
   */
  private async processAsset(assetId: string, versionId: string): Promise<void> {
    try {
      const assetVersion = await this.assetVersionRepository.findOne({
        where: { id: versionId },
      });

      if (!assetVersion) {
        return;
      }

      const asset = await this.assetRepository.findOne({
        where: { id: assetId },
      });

      if (!asset) {
        return;
      }

      // 根据资产类型进行处理
      switch (asset.type) {
        case AssetType.MODEL:
          // 处理3D模型
          // TODO: 实现模型处理逻辑
          break;
        case AssetType.TEXTURE:
          // 处理纹理
          // TODO: 实现纹理处理逻辑
          break;
        case AssetType.AUDIO:
          // 处理音频
          // TODO: 实现音频处理逻辑
          break;
        default:
          // 其他类型不需要处理
          break;
      }

      // 更新版本状态
      assetVersion.status = AssetStatus.READY;
      await this.assetVersionRepository.save(assetVersion);

      // 更新资产状态
      asset.status = AssetStatus.READY;
      await this.assetRepository.save(asset);
    } catch (error) {
      // 处理失败
      try {
        const assetVersion = await this.assetVersionRepository.findOne({
          where: { id: versionId },
        });

        if (assetVersion) {
          assetVersion.status = AssetStatus.ERROR;
          assetVersion.processingError = error.message;
          await this.assetVersionRepository.save(assetVersion);
        }

        const asset = await this.assetRepository.findOne({
          where: { id: assetId },
        });

        if (asset) {
          asset.status = AssetStatus.ERROR;
          await this.assetRepository.save(asset);
        }
      } catch (e) {
        // 忽略错误
      }
    }
  }

  /**
   * 获取资产版本
   */
  async getVersion(assetId: string, versionId: string, userId: string): Promise<AssetVersion> {
    // 检查资产权限
    await this.findOne(assetId, userId);

    const version = await this.assetVersionRepository.findOne({
      where: { id: versionId, assetId },
    });

    if (!version) {
      throw new NotFoundException(`版本ID ${versionId} 不存在`);
    }

    return version;
  }

  /**
   * 获取资产文件路径
   */
  async getAssetFilePath(assetId: string, versionId: string, userId: string): Promise<string> {
    const version = await this.getVersion(assetId, versionId, userId);
    return version.fileUrl;
  }

  /**
   * 搜索资产
   */
  async search(query: string, userId: string, type?: AssetType): Promise<Asset[]> {
    const queryBuilder = this.assetRepository.createQueryBuilder('asset')
      .leftJoinAndSelect('asset.versions', 'version')
      .leftJoinAndSelect('asset.tags', 'tag')
      .where('(asset.name LIKE :query OR asset.description LIKE :query OR tag.name LIKE :query)', { query: `%${query}%` })
      .andWhere('(asset.ownerId = :userId OR asset.projectId IS NULL)', { userId });

    if (type) {
      queryBuilder.andWhere('asset.type = :type', { type });
    }

    return queryBuilder.getMany();
  }
}
