/**
 * 用户服务测试
 */
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { ClientProxy } from '@nestjs/microservices';
import { EventBusService } from '../../../shared/event-bus';
import { UsersService } from './users.service';
import { UserEntity } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserTransactionService } from './user-transaction.service';

// 模拟数据
const mockUsers = [
  {
    id: '1',
    username: 'testuser1',
    email: '<EMAIL>',
    password: 'hashedpassword1',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    username: 'testuser2',
    email: '<EMAIL>',
    password: 'hashedpassword2',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// 模拟仓库
const mockUserRepository = {
  find: jest.fn().mockResolvedValue(mockUsers),
  findOne: jest.fn().mockImplementation(({ where }) => {
    if (where.id === '1') {
      return Promise.resolve(mockUsers[0]);
    }
    if (where.username === 'testuser1') {
      return Promise.resolve(mockUsers[0]);
    }
    if (where.email === '<EMAIL>') {
      return Promise.resolve(mockUsers[0]);
    }
    return Promise.resolve(null);
  }),
  create: jest.fn().mockImplementation((dto) => dto),
  save: jest.fn().mockImplementation((user) => Promise.resolve({ id: '3', ...user })),
  update: jest.fn().mockResolvedValue(true),
  delete: jest.fn().mockResolvedValue(true),
};

// 模拟JWT服务
const mockJwtService = {
  sign: jest.fn().mockReturnValue('mock.jwt.token'),
};

// 模拟配置服务
const mockConfigService = {
  get: jest.fn().mockImplementation((key) => {
    if (key === 'JWT_SECRET') return 'test-secret';
    if (key === 'JWT_EXPIRES_IN') return '1d';
    return null;
  }),
};

// 模拟事件总线服务
const mockEventBusService = {
  publish: jest.fn().mockResolvedValue(undefined),
};

// 模拟微服务客户端
const mockServiceRegistry = {
  send: jest.fn().mockResolvedValue([{ id: '1', name: 'service-registry' }]),
};

// 模拟事务服务
const mockUserTransactionService = {
  prepare: jest.fn().mockResolvedValue(true),
  commit: jest.fn().mockResolvedValue(true),
  rollback: jest.fn().mockResolvedValue(true),
};

describe('UsersService', () => {
  let service: UsersService;
  let userRepository: Repository<UserEntity>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(UserEntity),
          useValue: mockUserRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EventBusService,
          useValue: mockEventBusService,
        },
        {
          provide: 'SERVICE_REGISTRY',
          useValue: mockServiceRegistry,
        },
        {
          provide: UserTransactionService,
          useValue: mockUserTransactionService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userRepository = module.get<Repository<UserEntity>>(getRepositoryToken(UserEntity));
  });

  it('应该被定义', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('应该返回所有用户', async () => {
      const result = await service.findAll();
      expect(result).toEqual(mockUsers);
      expect(userRepository.find).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('应该通过ID返回单个用户', async () => {
      const result = await service.findOne('1');
      expect(result).toEqual(mockUsers[0]);
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { id: '1' } });
    });

    it('当用户不存在时应该抛出异常', async () => {
      await expect(service.findOne('999')).rejects.toThrow('用户不存在');
    });
  });

  describe('findByUsername', () => {
    it('应该通过用户名返回用户', async () => {
      const result = await service.findByUsername('testuser1');
      expect(result).toEqual(mockUsers[0]);
      expect(userRepository.findOne).toHaveBeenCalledWith({ where: { username: 'testuser1' } });
    });
  });

  describe('create', () => {
    it('应该创建并返回新用户', async () => {
      const createUserDto: CreateUserDto = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = await service.create(createUserDto);
      
      expect(result).toHaveProperty('id');
      expect(result.username).toEqual(createUserDto.username);
      expect(result.email).toEqual(createUserDto.email);
      expect(userRepository.create).toHaveBeenCalled();
      expect(userRepository.save).toHaveBeenCalled();
      expect(mockEventBusService.publish).toHaveBeenCalledWith('user.created', expect.any(Object));
    });

    it('当用户名已存在时应该抛出异常', async () => {
      const createUserDto: CreateUserDto = {
        username: 'testuser1', // 已存在的用户名
        email: '<EMAIL>',
        password: 'password123',
      };

      await expect(service.create(createUserDto)).rejects.toThrow('用户名已存在');
    });

    it('当邮箱已存在时应该抛出异常', async () => {
      const createUserDto: CreateUserDto = {
        username: 'newuser',
        email: '<EMAIL>', // 已存在的邮箱
        password: 'password123',
      };

      await expect(service.create(createUserDto)).rejects.toThrow('邮箱已存在');
    });
  });

  describe('update', () => {
    it('应该更新并返回用户', async () => {
      const updateUserDto: UpdateUserDto = {
        username: 'updateduser',
        email: '<EMAIL>',
      };

      jest.spyOn(service, 'findOne').mockResolvedValueOnce(mockUsers[0]);
      
      const result = await service.update('1', updateUserDto);
      
      expect(result).toBeDefined();
      expect(userRepository.save).toHaveBeenCalled();
      expect(mockEventBusService.publish).toHaveBeenCalledWith('user.updated', expect.any(Object));
    });
  });

  describe('remove', () => {
    it('应该删除用户', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValueOnce(mockUsers[0]);
      
      await service.remove('1');
      
      expect(userRepository.delete).toHaveBeenCalledWith('1');
      expect(mockEventBusService.publish).toHaveBeenCalledWith('user.deleted', expect.any(Object));
    });
  });

  describe('validateUser', () => {
    it('当凭据有效时应该返回用户', async () => {
      jest.spyOn(service, 'findByUsername').mockResolvedValueOnce(mockUsers[0]);
      jest.spyOn(service as any, 'comparePasswords').mockResolvedValueOnce(true);
      
      const result = await service.validateUser('testuser1', 'password123');
      
      expect(result).toEqual({
        id: mockUsers[0].id,
        username: mockUsers[0].username,
        email: mockUsers[0].email,
      });
    });

    it('当凭据无效时应该返回null', async () => {
      jest.spyOn(service, 'findByUsername').mockResolvedValueOnce(mockUsers[0]);
      jest.spyOn(service as any, 'comparePasswords').mockResolvedValueOnce(false);
      
      const result = await service.validateUser('testuser1', 'wrongpassword');
      
      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    it('应该返回访问令牌', async () => {
      const user = {
        id: '1',
        username: 'testuser1',
        email: '<EMAIL>',
      };
      
      const result = await service.login(user);
      
      expect(result).toEqual({ accessToken: 'mock.jwt.token' });
      expect(mockJwtService.sign).toHaveBeenCalledWith({
        sub: user.id,
        username: user.username,
        email: user.email,
      });
    });
  });
});
