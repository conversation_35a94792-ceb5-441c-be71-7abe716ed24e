import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm';
import { AlertRuleEntity } from './alert-rule.entity';

export enum AlertSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export enum AlertStatus {
  ACTIVE = 'active',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
}

@Entity('alerts')
export class AlertEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  name: string;

  @Column('text')
  description: string;

  @Column({
    type: 'enum',
    enum: AlertSeverity,
    default: AlertSeverity.WARNING,
  })
  @Index()
  severity: AlertSeverity;

  @Column({
    type: 'enum',
    enum: AlertStatus,
    default: AlertStatus.ACTIVE,
  })
  @Index()
  status: AlertStatus;

  @Column({ nullable: true })
  @Index()
  serviceId: string;

  @Column({ nullable: true })
  @Index()
  serviceType: string;

  @Column({ nullable: true })
  @Index()
  instanceId: string;

  @Column({ nullable: true })
  @Index()
  hostname: string;

  @Column('json')
  labels: Record<string, string>;

  @Column('json')
  annotations: Record<string, string>;

  @Column('json')
  value: Record<string, any>;

  @Column({ nullable: true })
  @Index()
  ruleId: string;

  @ManyToOne(() => AlertRuleEntity, { nullable: true })
  @JoinColumn({ name: 'ruleId' })
  rule: AlertRuleEntity;

  @Column('timestamp')
  @Index()
  startTime: Date;

  @Column('timestamp', { nullable: true })
  endTime: Date;

  @Column({ nullable: true })
  acknowledgedBy: string;

  @Column('timestamp', { nullable: true })
  acknowledgedAt: Date;

  @Column({ nullable: true })
  resolvedBy: string;

  @Column('timestamp', { nullable: true })
  resolvedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
