/**
 * 资产模块
 */
import { Module } from '@nestjs/common';
import { AssetsController } from './assets.controller';
import { AssetsService } from './assets.service';
import { ModelsController } from './models.controller';
import { TexturesController } from './textures.controller';
import { AudioController } from './audio.controller';

@Module({
  controllers: [AssetsController, ModelsController, TexturesController, AudioController],
  providers: [AssetsService],
  exports: [AssetsService],
})
export class AssetsModule {}
