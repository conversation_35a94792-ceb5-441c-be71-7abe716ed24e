/**
 * 资产控制器
 */
import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request, 
  Query, 
  UseInterceptors, 
  UploadedFile,
  Res
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { AssetsService } from './assets.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('资产')
@Controller('assets')
export class AssetsController {
  constructor(private readonly assetsService: AssetsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建资产' })
  @ApiResponse({ status: 201, description: '资产创建成功' })
  async create(@Request() req, @Body() createAssetDto: any) {
    return this.assetsService.create(req.user.id, createAssetDto);
  }

  @Post('upload')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '上传资产文件' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ status: 201, description: '资产上传成功' })
  async upload(@Request() req, @UploadedFile() file: any, @Body() createAssetDto: any) {
    return this.assetsService.upload(req.user.id, file, createAssetDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有资产' })
  @ApiResponse({ status: 200, description: '返回所有资产' })
  async findAll(
    @Request() req,
    @Query('projectId') projectId?: string,
    @Query('type') type?: string,
    @Query('tags') tags?: string,
  ) {
    const tagArray = tags ? tags.split(',') : undefined;
    return this.assetsService.findAll(req.user.id, projectId, type, tagArray);
  }

  @Get('search')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '搜索资产' })
  @ApiResponse({ status: 200, description: '返回搜索结果' })
  async search(
    @Request() req,
    @Query('query') query: string,
    @Query('type') type?: string,
  ) {
    return this.assetsService.search(req.user.id, query, type);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取资产' })
  @ApiResponse({ status: 200, description: '返回资产信息' })
  async findOne(@Param('id') id: string, @Request() req) {
    return this.assetsService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新资产' })
  @ApiResponse({ status: 200, description: '资产更新成功' })
  async update(@Param('id') id: string, @Request() req, @Body() updateAssetDto: any) {
    return this.assetsService.update(id, req.user.id, updateAssetDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除资产' })
  @ApiResponse({ status: 204, description: '资产删除成功' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.assetsService.remove(id, req.user.id);
  }

  @Post(':id/versions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '上传新版本' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ status: 201, description: '新版本上传成功' })
  async uploadVersion(@Param('id') id: string, @Request() req, @UploadedFile() file: any) {
    return this.assetsService.uploadVersion(id, req.user.id, file);
  }

  @Get(':id/versions/:versionId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取资产版本' })
  @ApiResponse({ status: 200, description: '返回资产版本信息' })
  async getVersion(
    @Param('id') id: string,
    @Param('versionId') versionId: string,
    @Request() req,
  ) {
    return this.assetsService.getVersion(id, versionId, req.user.id);
  }

  @Get(':id/versions/:versionId/download')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '下载资产文件' })
  @ApiResponse({ status: 200, description: '返回资产文件' })
  async downloadFile(
    @Param('id') id: string,
    @Param('versionId') versionId: string,
    @Request() req,
    @Res() res: Response,
  ) {
    const filePath = await this.assetsService.getAssetFilePath(id, versionId, req.user.id);
    return res.download(filePath);
  }
}
