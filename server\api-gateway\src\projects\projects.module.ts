/**
 * 项目模块
 */
import { Module } from '@nestjs/common';
import { ProjectsController } from './projects.controller';
import { ProjectsService } from './projects.service';
import { ScenesController } from './scenes.controller';
import { ScenesService } from './scenes.service';

@Module({
  controllers: [ProjectsController, ScenesController],
  providers: [ProjectsService, ScenesService],
  exports: [ProjectsService, ScenesService],
})
export class ProjectsModule {}
