import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InstanceService } from './instance.service';
import { InstanceMigrationService } from './instance-migration.service';
import { Instance } from './instance.interface';

/**
 * 负载均衡服务
 * 负责监控和平衡游戏实例的负载
 */
@Injectable()
export class LoadBalancerService {
  private readonly logger = new Logger(LoadBalancerService.name);
  private readonly highLoadThreshold: number;
  private readonly lowLoadThreshold: number;
  private readonly balancingEnabled: boolean;
  private readonly instanceMetrics: Map<string, {
    cpuUsage: number;
    memoryUsage: number;
    networkUsage: number;
    lastUpdated: Date;
  }> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly instanceService: InstanceService,
    private readonly instanceMigrationService: InstanceMigrationService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.highLoadThreshold = this.configService.get<number>('INSTANCE_HIGH_LOAD_THRESHOLD', 0.8); // 80%
    this.lowLoadThreshold = this.configService.get<number>('INSTANCE_LOW_LOAD_THRESHOLD', 0.2); // 20%
    this.balancingEnabled = this.configService.get<boolean>('ENABLE_LOAD_BALANCING', true);
    
    // 监听实例指标更新事件
    this.eventEmitter.on('instance.metrics', this.handleInstanceMetrics.bind(this));
  }

  /**
   * 处理实例指标更新事件
   * @param data 事件数据
   */
  private handleInstanceMetrics(data: {
    instanceId: string;
    cpuUsage: number;
    memoryUsage: number;
    networkUsage: number;
  }): void {
    const { instanceId, cpuUsage, memoryUsage, networkUsage } = data;
    
    // 更新实例指标
    this.instanceMetrics.set(instanceId, {
      cpuUsage,
      memoryUsage,
      networkUsage,
      lastUpdated: new Date(),
    });
  }

  /**
   * 定时检查实例负载并进行平衡
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async checkAndBalanceLoad(): Promise<void> {
    if (!this.balancingEnabled) {
      return;
    }
    
    this.logger.debug('开始检查实例负载...');
    
    try {
      // 获取所有实例
      const instances = this.instanceService.getAllInstances();
      
      // 过滤出高负载和低负载实例
      const highLoadInstances = this.getHighLoadInstances(instances);
      const lowLoadInstances = this.getLowLoadInstances(instances);
      
      this.logger.debug(`发现 ${highLoadInstances.length} 个高负载实例和 ${lowLoadInstances.length} 个低负载实例`);
      
      // 处理高负载实例
      for (const instance of highLoadInstances) {
        await this.balanceHighLoadInstance(instance, lowLoadInstances);
      }
      
      // 处理低负载实例
      if (lowLoadInstances.length >= 2) {
        await this.consolidateLowLoadInstances(lowLoadInstances);
      }
    } catch (error) {
      this.logger.error(`负载均衡过程中出错: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取高负载实例
   * @param instances 实例列表
   * @returns 高负载实例列表
   */
  private getHighLoadInstances(instances: Instance[]): Instance[] {
    return instances.filter(instance => {
      // 获取实例指标
      const metrics = this.instanceMetrics.get(instance.id);
      
      if (!metrics) {
        return false;
      }
      
      // 检查是否为高负载
      const isHighCpuLoad = metrics.cpuUsage > this.highLoadThreshold;
      const isHighMemoryLoad = metrics.memoryUsage > this.highLoadThreshold;
      const isHighUserLoad = instance.currentUsers > instance.maxUsers * this.highLoadThreshold;
      
      return isHighCpuLoad || isHighMemoryLoad || isHighUserLoad;
    });
  }

  /**
   * 获取低负载实例
   * @param instances 实例列表
   * @returns 低负载实例列表
   */
  private getLowLoadInstances(instances: Instance[]): Instance[] {
    return instances.filter(instance => {
      // 获取实例指标
      const metrics = this.instanceMetrics.get(instance.id);
      
      if (!metrics) {
        return false;
      }
      
      // 检查是否为低负载
      const isLowCpuLoad = metrics.cpuUsage < this.lowLoadThreshold;
      const isLowMemoryLoad = metrics.memoryUsage < this.lowLoadThreshold;
      const isLowUserLoad = instance.currentUsers < instance.maxUsers * this.lowLoadThreshold;
      
      // 所有指标都是低负载
      return isLowCpuLoad && isLowMemoryLoad && isLowUserLoad;
    });
  }

  /**
   * 平衡高负载实例
   * @param instance 高负载实例
   * @param lowLoadInstances 低负载实例列表
   */
  private async balanceHighLoadInstance(instance: Instance, lowLoadInstances: Instance[]): Promise<void> {
    this.logger.log(`平衡高负载实例: ${instance.id}, 当前用户数: ${instance.currentUsers}`);
    
    // 查找合适的目标实例
    const targetInstance = this.findSuitableTargetInstance(instance, lowLoadInstances);
    
    if (!targetInstance) {
      // 如果没有合适的目标实例，创建一个新实例
      try {
        const newInstance = await this.instanceService.createInstance({
          sceneId: instance.sceneId,
          locationId: instance.locationId,
          channelId: instance.channelId,
          isMediaInstance: instance.isMediaInstance,
        });
        
        this.logger.log(`为高负载实例 ${instance.id} 创建了新实例 ${newInstance.id}`);
        
        // 迁移部分用户到新实例
        await this.migrateUsers(instance, newInstance);
      } catch (error) {
        this.logger.error(`创建新实例失败: ${error.message}`, error.stack);
      }
    } else {
      // 迁移部分用户到目标实例
      await this.migrateUsers(instance, targetInstance);
    }
  }

  /**
   * 查找合适的目标实例
   * @param sourceInstance 源实例
   * @param candidates 候选实例列表
   * @returns 目标实例
   */
  private findSuitableTargetInstance(sourceInstance: Instance, candidates: Instance[]): Instance | null {
    // 过滤出符合条件的实例
    const suitableInstances = candidates.filter(instance => 
      // 相同的场景ID
      instance.sceneId === sourceInstance.sceneId &&
      // 有足够的容量
      instance.currentUsers + Math.ceil(sourceInstance.currentUsers * 0.3) <= instance.maxUsers
    );
    
    if (suitableInstances.length === 0) {
      return null;
    }
    
    // 选择负载最小的实例
    return suitableInstances.reduce((prev, curr) => {
      const prevMetrics = this.instanceMetrics.get(prev.id);
      const currMetrics = this.instanceMetrics.get(curr.id);
      
      if (!prevMetrics || !currMetrics) {
        return prev.currentUsers < curr.currentUsers ? prev : curr;
      }
      
      // 计算综合负载分数
      const prevScore = prevMetrics.cpuUsage * 0.4 + prevMetrics.memoryUsage * 0.3 + (prev.currentUsers / prev.maxUsers) * 0.3;
      const currScore = currMetrics.cpuUsage * 0.4 + currMetrics.memoryUsage * 0.3 + (curr.currentUsers / curr.maxUsers) * 0.3;
      
      return prevScore < currScore ? prev : curr;
    });
  }

  /**
   * 迁移用户
   * @param sourceInstance 源实例
   * @param targetInstance 目标实例
   */
  private async migrateUsers(sourceInstance: Instance, targetInstance: Instance): Promise<void> {
    // 计算要迁移的用户数量（约30%）
    const userCount = Math.ceil(sourceInstance.currentUsers * 0.3);
    
    if (userCount <= 0) {
      return;
    }
    
    this.logger.log(`从实例 ${sourceInstance.id} 迁移 ${userCount} 个用户到实例 ${targetInstance.id}`);
    
    try {
      // 获取源实例中的用户
      const users = await this.instanceService.getInstanceUsers(sourceInstance.id);
      
      // 选择要迁移的用户
      const usersToMigrate = users.slice(0, userCount);
      
      // 创建部分迁移
      const migrationId = await this.instanceMigrationService.startPartialMigration(
        sourceInstance.id,
        targetInstance.id,
        usersToMigrate
      );
      
      this.logger.log(`已启动部分迁移 ${migrationId}`);
    } catch (error) {
      this.logger.error(`迁移用户失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 合并低负载实例
   * @param lowLoadInstances 低负载实例列表
   */
  private async consolidateLowLoadInstances(lowLoadInstances: Instance[]): Promise<void> {
    if (lowLoadInstances.length < 2) {
      return;
    }
    
    this.logger.log(`尝试合并 ${lowLoadInstances.length} 个低负载实例`);
    
    // 按场景ID分组
    const instancesByScene = new Map<string, Instance[]>();
    
    for (const instance of lowLoadInstances) {
      if (!instance.sceneId) {
        continue;
      }
      
      const instances = instancesByScene.get(instance.sceneId) || [];
      instances.push(instance);
      instancesByScene.set(instance.sceneId, instances);
    }
    
    // 处理每个场景的实例
    for (const [sceneId, instances] of instancesByScene.entries()) {
      if (instances.length < 2) {
        continue;
      }
      
      // 按用户数排序
      instances.sort((a, b) => b.currentUsers - a.currentUsers);
      
      // 选择用户数最多的实例作为目标实例
      const targetInstance = instances[0];
      
      // 合并其他实例到目标实例
      for (let i = 1; i < instances.length; i++) {
        const sourceInstance = instances[i];
        
        // 检查目标实例是否有足够的容量
        if (targetInstance.currentUsers + sourceInstance.currentUsers <= targetInstance.maxUsers) {
          try {
            // 启动完整迁移
            const migrationId = await this.instanceMigrationService.startMigration(
              sourceInstance.id,
              targetInstance.id
            );
            
            this.logger.log(`已启动从实例 ${sourceInstance.id} 到实例 ${targetInstance.id} 的完整迁移 ${migrationId}`);
            
            // 更新目标实例的用户数
            targetInstance.currentUsers += sourceInstance.currentUsers;
            
            // 标记源实例为即将关闭
            sourceInstance.status = 'closing';
          } catch (error) {
            this.logger.error(`合并实例失败: ${error.message}`, error.stack);
          }
        }
      }
    }
  }

  /**
   * 获取实例负载信息
   * @param instanceId 实例ID
   */
  getInstanceLoad(instanceId: string): any {
    const instance = this.instanceService.getInstance(instanceId);
    
    if (!instance) {
      return null;
    }
    
    const metrics = this.instanceMetrics.get(instanceId);
    
    if (!metrics) {
      return {
        id: instanceId,
        userLoad: instance.currentUsers / instance.maxUsers,
        cpuUsage: null,
        memoryUsage: null,
        networkUsage: null,
        lastUpdated: null,
      };
    }
    
    return {
      id: instanceId,
      userLoad: instance.currentUsers / instance.maxUsers,
      cpuUsage: metrics.cpuUsage,
      memoryUsage: metrics.memoryUsage,
      networkUsage: metrics.networkUsage,
      lastUpdated: metrics.lastUpdated,
    };
  }

  /**
   * 获取所有实例的负载信息
   */
  getAllInstancesLoad(): any[] {
    const instances = this.instanceService.getAllInstances();
    
    return instances.map(instance => this.getInstanceLoad(instance.id)).filter(load => load !== null);
  }
}
