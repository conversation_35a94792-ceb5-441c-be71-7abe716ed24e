/**
 * 创建资产DTO
 */
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsObject, IsArray, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AssetType } from '../entities/asset.entity';

export class CreateAssetDto {
  @ApiProperty({ description: '资产名称', example: '立方体模型' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '资产描述', required: false, example: '这是一个立方体3D模型' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '资产类型', enum: AssetType, default: AssetType.OTHER })
  @IsEnum(AssetType)
  @IsOptional()
  type?: AssetType;

  @ApiProperty({ description: '缩略图URL', required: false })
  @IsString()
  @IsOptional()
  thumbnailUrl?: string;

  @ApiProperty({ description: '项目ID', required: false })
  @IsUUID()
  @IsOptional()
  projectId?: string;

  @ApiProperty({ description: '元数据', required: false, type: 'object' })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiProperty({ description: '标签', required: false, type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}
