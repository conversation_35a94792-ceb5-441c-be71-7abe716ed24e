import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AlertService } from './alert.service';
import { AlertController } from './alert.controller';
import { AlertEntity } from './entities/alert.entity';
import { AlertRuleEntity } from './entities/alert-rule.entity';
import { AlertRuleService } from './alert-rule.service';
import { AlertEvaluatorService } from './alert-evaluator.service';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AlertEntity, AlertRuleEntity]),
    NotificationModule,
  ],
  controllers: [AlertController],
  providers: [
    AlertService,
    AlertRuleService,
    AlertEvaluatorService,
  ],
  exports: [
    AlertService,
    AlertRuleService,
    AlertEvaluatorService,
  ],
})
export class AlertModule {}
