import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceMetricsEntity } from './entities/service-metrics.entity';
import { SystemMetricsEntity } from './entities/system-metrics.entity';

@Injectable()
export class MetricsAggregatorService {
  private readonly logger = new Logger(MetricsAggregatorService.name);

  constructor(
    @InjectRepository(ServiceMetricsEntity)
    private readonly serviceMetricsRepository: Repository<ServiceMetricsEntity>,
    @InjectRepository(SystemMetricsEntity)
    private readonly systemMetricsRepository: Repository<SystemMetricsEntity>,
  ) {}

  /**
   * 聚合服务指标
   */
  async aggregateServiceMetrics(startTime: Date, endTime: Date): Promise<void> {
    try {
      // 获取所有服务ID
      const services = await this.serviceMetricsRepository
        .createQueryBuilder('metrics')
        .select('DISTINCT metrics.serviceId, metrics.serviceType')
        .where('metrics.timestamp BETWEEN :startTime AND :endTime', { startTime, endTime })
        .getRawMany();
      
      for (const service of services) {
        // 获取服务的所有实例
        const instances = await this.serviceMetricsRepository
          .createQueryBuilder('metrics')
          .select('DISTINCT metrics.instanceId, metrics.hostname')
          .where('metrics.serviceId = :serviceId', { serviceId: service.serviceId })
          .andWhere('metrics.timestamp BETWEEN :startTime AND :endTime', { startTime, endTime })
          .getRawMany();
        
        for (const instance of instances) {
          // 获取实例的指标
          const metrics = await this.serviceMetricsRepository.find({
            where: {
              serviceId: service.serviceId,
              instanceId: instance.instanceId,
              timestamp: { $gte: startTime, $lte: endTime },
            },
            order: { timestamp: 'ASC' },
          });
          
          if (metrics.length === 0) {
            continue;
          }
          
          // 计算聚合指标
          const aggregatedMetrics = this.calculateAggregatedServiceMetrics(metrics);
          
          // 存储聚合指标
          const aggregatedEntity = new ServiceMetricsEntity();
          aggregatedEntity.serviceId = service.serviceId;
          aggregatedEntity.serviceType = service.serviceType;
          aggregatedEntity.instanceId = instance.instanceId;
          aggregatedEntity.hostname = instance.hostname;
          aggregatedEntity.requestCount = aggregatedMetrics.requestCount;
          aggregatedEntity.errorCount = aggregatedMetrics.errorCount;
          aggregatedEntity.errorRate = aggregatedMetrics.errorRate;
          aggregatedEntity.averageResponseTime = aggregatedMetrics.averageResponseTime;
          aggregatedEntity.activeConnections = aggregatedMetrics.activeConnections;
          aggregatedEntity.queueLength = aggregatedMetrics.queueLength;
          aggregatedEntity.customMetrics = aggregatedMetrics.customMetrics;
          aggregatedEntity.timestamp = new Date(endTime.getTime());
          
          await this.serviceMetricsRepository.save(aggregatedEntity);
        }
      }
    } catch (error) {
      this.logger.error(`聚合服务指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 聚合系统指标
   */
  async aggregateSystemMetrics(startTime: Date, endTime: Date): Promise<void> {
    try {
      // 获取所有主机名
      const hosts = await this.systemMetricsRepository
        .createQueryBuilder('metrics')
        .select('DISTINCT metrics.hostname')
        .where('metrics.timestamp BETWEEN :startTime AND :endTime', { startTime, endTime })
        .getRawMany();
      
      for (const host of hosts) {
        // 获取主机的指标
        const metrics = await this.systemMetricsRepository.find({
          where: {
            hostname: host.hostname,
            timestamp: { $gte: startTime, $lte: endTime },
          },
          order: { timestamp: 'ASC' },
        });
        
        if (metrics.length === 0) {
          continue;
        }
        
        // 计算聚合指标
        const aggregatedMetrics = this.calculateAggregatedSystemMetrics(metrics);
        
        // 存储聚合指标
        const aggregatedEntity = new SystemMetricsEntity();
        aggregatedEntity.hostname = host.hostname;
        aggregatedEntity.cpuUsage = aggregatedMetrics.cpuUsage;
        aggregatedEntity.memoryUsage = aggregatedMetrics.memoryUsage;
        aggregatedEntity.totalMemory = aggregatedMetrics.totalMemory;
        aggregatedEntity.freeMemory = aggregatedMetrics.freeMemory;
        aggregatedEntity.diskUsage = aggregatedMetrics.diskUsage;
        aggregatedEntity.totalDisk = aggregatedMetrics.totalDisk;
        aggregatedEntity.freeDisk = aggregatedMetrics.freeDisk;
        aggregatedEntity.loadAverage = aggregatedMetrics.loadAverage;
        aggregatedEntity.networkConnections = aggregatedMetrics.networkConnections;
        aggregatedEntity.timestamp = new Date(endTime.getTime());
        
        await this.systemMetricsRepository.save(aggregatedEntity);
      }
    } catch (error) {
      this.logger.error(`聚合系统指标失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 计算聚合的服务指标
   */
  private calculateAggregatedServiceMetrics(metrics: ServiceMetricsEntity[]): any {
    const totalRequestCount = metrics.reduce((sum, m) => sum + m.requestCount, 0);
    const totalErrorCount = metrics.reduce((sum, m) => sum + m.errorCount, 0);
    const totalResponseTime = metrics.reduce((sum, m) => sum + (m.averageResponseTime * m.requestCount), 0);
    
    return {
      requestCount: totalRequestCount,
      errorCount: totalErrorCount,
      errorRate: totalRequestCount > 0 ? totalErrorCount / totalRequestCount : 0,
      averageResponseTime: totalRequestCount > 0 ? totalResponseTime / totalRequestCount : 0,
      activeConnections: this.calculateAverage(metrics, 'activeConnections'),
      queueLength: this.calculateAverage(metrics, 'queueLength'),
      customMetrics: this.aggregateCustomMetrics(metrics),
    };
  }

  /**
   * 计算聚合的系统指标
   */
  private calculateAggregatedSystemMetrics(metrics: SystemMetricsEntity[]): any {
    return {
      cpuUsage: this.calculateAverage(metrics, 'cpuUsage'),
      memoryUsage: this.calculateAverage(metrics, 'memoryUsage'),
      totalMemory: metrics[metrics.length - 1].totalMemory,
      freeMemory: metrics[metrics.length - 1].freeMemory,
      diskUsage: this.calculateAverage(metrics, 'diskUsage'),
      totalDisk: metrics[metrics.length - 1].totalDisk,
      freeDisk: metrics[metrics.length - 1].freeDisk,
      loadAverage: [
        this.calculateAverage(metrics, m => m.loadAverage[0]),
        this.calculateAverage(metrics, m => m.loadAverage[1]),
        this.calculateAverage(metrics, m => m.loadAverage[2]),
      ],
      networkConnections: this.calculateAverage(metrics, 'networkConnections'),
    };
  }

  /**
   * 计算平均值
   */
  private calculateAverage<T>(items: T[], property: keyof T | ((item: T) => number)): number {
    if (items.length === 0) {
      return 0;
    }
    
    let sum = 0;
    
    if (typeof property === 'function') {
      sum = items.reduce((acc, item) => acc + property(item), 0);
    } else {
      sum = items.reduce((acc, item) => acc + (item[property] as any), 0);
    }
    
    return sum / items.length;
  }

  /**
   * 聚合自定义指标
   */
  private aggregateCustomMetrics(metrics: ServiceMetricsEntity[]): Record<string, any> {
    const customMetrics: Record<string, any> = {};
    
    if (metrics.length === 0 || !metrics[0].customMetrics) {
      return customMetrics;
    }
    
    // 获取所有自定义指标的键
    const keys = new Set<string>();
    
    for (const metric of metrics) {
      if (metric.customMetrics) {
        for (const key in metric.customMetrics) {
          keys.add(key);
        }
      }
    }
    
    // 计算每个自定义指标的平均值
    for (const key of keys) {
      const values = metrics
        .filter(m => m.customMetrics && m.customMetrics[key] !== undefined)
        .map(m => m.customMetrics![key]);
      
      if (values.length > 0) {
        if (typeof values[0] === 'number') {
          // 数值类型，计算平均值
          customMetrics[key] = values.reduce((sum, val) => sum + val, 0) / values.length;
        } else {
          // 非数值类型，使用最后一个值
          customMetrics[key] = values[values.length - 1];
        }
      }
    }
    
    return customMetrics;
  }
}
