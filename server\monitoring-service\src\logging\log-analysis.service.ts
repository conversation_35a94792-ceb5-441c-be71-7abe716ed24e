import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LogEntity, LogLevel } from './entities/log.entity';

interface LogPattern {
  id: string;
  name: string;
  pattern: RegExp;
  level: LogLevel;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  actionable: boolean;
  tags: string[];
}

@Injectable()
export class LogAnalysisService {
  private readonly logger = new Logger(LogAnalysisService.name);
  private readonly enabled: boolean;
  private readonly patterns: LogPattern[] = [];

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.enabled = this.configService.get<boolean>('LOG_ANALYSIS_ENABLED', true);
    
    // 初始化日志模式
    this.initPatterns();
  }

  /**
   * 初始化日志模式
   */
  private initPatterns(): void {
    // 错误模式
    this.patterns.push(
      {
        id: 'error-exception',
        name: '异常错误',
        pattern: /exception|error|failed|failure|timeout|timed out/i,
        level: LogLevel.ERROR,
        description: '检测到异常或错误',
        severity: 'high',
        actionable: true,
        tags: ['error', 'exception'],
      },
      {
        id: 'error-database',
        name: '数据库错误',
        pattern: /database|db|sql|query|connection|mysql|postgres/i,
        level: LogLevel.ERROR,
        description: '检测到数据库相关错误',
        severity: 'high',
        actionable: true,
        tags: ['error', 'database'],
      },
      {
        id: 'error-network',
        name: '网络错误',
        pattern: /network|connection|timeout|unreachable|refused|reset|closed/i,
        level: LogLevel.ERROR,
        description: '检测到网络相关错误',
        severity: 'high',
        actionable: true,
        tags: ['error', 'network'],
      },
      {
        id: 'error-memory',
        name: '内存错误',
        pattern: /memory|out of memory|allocation|heap|stack|overflow/i,
        level: LogLevel.ERROR,
        description: '检测到内存相关错误',
        severity: 'critical',
        actionable: true,
        tags: ['error', 'memory'],
      },
      {
        id: 'error-disk',
        name: '磁盘错误',
        pattern: /disk|storage|file|io|read|write|permission|access denied/i,
        level: LogLevel.ERROR,
        description: '检测到磁盘或存储相关错误',
        severity: 'high',
        actionable: true,
        tags: ['error', 'disk'],
      },
      {
        id: 'error-authentication',
        name: '认证错误',
        pattern: /auth|authentication|login|password|credential|unauthorized|forbidden/i,
        level: LogLevel.ERROR,
        description: '检测到认证相关错误',
        severity: 'medium',
        actionable: true,
        tags: ['error', 'security', 'authentication'],
      },
      {
        id: 'error-authorization',
        name: '授权错误',
        pattern: /authorization|permission|access|denied|forbidden/i,
        level: LogLevel.ERROR,
        description: '检测到授权相关错误',
        severity: 'medium',
        actionable: true,
        tags: ['error', 'security', 'authorization'],
      },
    );
    
    // 警告模式
    this.patterns.push(
      {
        id: 'warning-performance',
        name: '性能警告',
        pattern: /slow|performance|latency|delay|timeout/i,
        level: LogLevel.WARN,
        description: '检测到性能相关警告',
        severity: 'medium',
        actionable: true,
        tags: ['warning', 'performance'],
      },
      {
        id: 'warning-resource',
        name: '资源警告',
        pattern: /resource|memory|cpu|disk|storage|usage|utilization|high/i,
        level: LogLevel.WARN,
        description: '检测到资源使用相关警告',
        severity: 'medium',
        actionable: true,
        tags: ['warning', 'resource'],
      },
      {
        id: 'warning-deprecated',
        name: '废弃警告',
        pattern: /deprecated|obsolete|removed|unsupported/i,
        level: LogLevel.WARN,
        description: '检测到使用废弃功能的警告',
        severity: 'low',
        actionable: false,
        tags: ['warning', 'deprecated'],
      },
    );
    
    // 安全模式
    this.patterns.push(
      {
        id: 'security-injection',
        name: '注入攻击',
        pattern: /injection|sql injection|xss|cross-site|script|attack/i,
        level: LogLevel.ERROR,
        description: '检测到可能的注入攻击',
        severity: 'critical',
        actionable: true,
        tags: ['security', 'attack', 'injection'],
      },
      {
        id: 'security-bruteforce',
        name: '暴力攻击',
        pattern: /brute force|bruteforce|multiple login|login attempt|failed login/i,
        level: LogLevel.ERROR,
        description: '检测到可能的暴力攻击',
        severity: 'high',
        actionable: true,
        tags: ['security', 'attack', 'bruteforce'],
      },
    );
  }

  /**
   * 分析日志
   */
  async analyzeLog(log: LogEntity): Promise<void> {
    if (!this.enabled) {
      return;
    }
    
    try {
      // 只分析警告和错误日志
      if (log.level !== LogLevel.WARN && log.level !== LogLevel.ERROR && log.level !== LogLevel.FATAL) {
        return;
      }
      
      // 匹配日志模式
      const matchedPatterns = this.matchPatterns(log);
      
      if (matchedPatterns.length === 0) {
        return;
      }
      
      // 触发日志分析事件
      for (const pattern of matchedPatterns) {
        this.eventEmitter.emit('logging.analysis.pattern.matched', {
          logId: log.id,
          pattern,
          log,
          timestamp: new Date(),
        });
        
        // 如果是高严重性或关键严重性，触发告警事件
        if (pattern.severity === 'high' || pattern.severity === 'critical') {
          this.eventEmitter.emit('logging.analysis.alert', {
            logId: log.id,
            pattern,
            log,
            timestamp: new Date(),
          });
        }
      }
    } catch (error) {
      this.logger.error(`分析日志失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 匹配日志模式
   */
  private matchPatterns(log: LogEntity): LogPattern[] {
    const matchedPatterns: LogPattern[] = [];
    const message = log.message || '';
    const stack = log.stack || '';
    const content = `${message}\n${stack}`;
    
    for (const pattern of this.patterns) {
      // 只匹配相同级别或更高级别的模式
      if (this.isLevelHigherOrEqual(log.level, pattern.level)) {
        if (pattern.pattern.test(content)) {
          matchedPatterns.push(pattern);
        }
      }
    }
    
    return matchedPatterns;
  }

  /**
   * 检查日志级别是否大于等于指定级别
   */
  private isLevelHigherOrEqual(level: LogLevel, targetLevel: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.FATAL];
    const levelIndex = levels.indexOf(level);
    const targetIndex = levels.indexOf(targetLevel);
    
    return levelIndex >= targetIndex;
  }

  /**
   * 获取所有日志模式
   */
  getPatterns(): LogPattern[] {
    return [...this.patterns];
  }

  /**
   * 添加自定义日志模式
   */
  addPattern(pattern: LogPattern): void {
    this.patterns.push(pattern);
  }
}
