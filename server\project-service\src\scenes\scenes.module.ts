/**
 * 场景模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScenesController } from './scenes.controller';
import { ScenesService } from './scenes.service';
import { Scene } from './entities/scene.entity';
import { SceneEntity } from './entities/scene-entity.entity';
import { ProjectsModule } from '../projects/projects.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Scene, SceneEntity]),
    ProjectsModule,
  ],
  controllers: [ScenesController],
  providers: [ScenesService],
  exports: [ScenesService],
})
export class ScenesModule {}
