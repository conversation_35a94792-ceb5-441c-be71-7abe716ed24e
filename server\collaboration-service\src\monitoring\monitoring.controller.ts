/**
 * 监控控制器
 * 提供监控API
 */
import { Controller, Get, Post, Body, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { MonitoringService, MonitoringMetrics, Alert } from './monitoring.service';
import { AuthGuard } from '@nestjs/passport';

/**
 * 监控控制器
 */
@ApiTags('监控')
@Controller('monitoring')
@UseGuards(AuthGuard('jwt'))
export class MonitoringController {
  /**
   * 构造函数
   * @param monitoringService 监控服务
   */
  constructor(private readonly monitoringService: MonitoringService) {}

  /**
   * 获取当前指标
   * @returns 当前指标
   */
  @Get('metrics/current')
  @ApiOperation({ summary: '获取当前指标' })
  @ApiResponse({ status: 200, description: '成功', type: Object })
  getCurrentMetrics(): MonitoringMetrics | null {
    return this.monitoringService.getCurrentMetrics();
  }

  /**
   * 获取历史指标
   * @param duration 时间范围（毫秒）
   * @returns 历史指标
   */
  @Get('metrics/history')
  @ApiOperation({ summary: '获取历史指标' })
  @ApiQuery({ name: 'duration', required: false, type: Number, description: '时间范围（毫秒）' })
  @ApiResponse({ status: 200, description: '成功', type: [Object] })
  getHistoryMetrics(@Query('duration') duration?: number): MonitoringMetrics[] {
    return this.monitoringService.getHistoryMetrics(duration);
  }

  /**
   * 获取活跃告警
   * @returns 活跃告警
   */
  @Get('alerts')
  @ApiOperation({ summary: '获取活跃告警' })
  @ApiResponse({ status: 200, description: '成功', type: [Object] })
  getActiveAlerts(): Alert[] {
    return this.monitoringService.getActiveAlerts();
  }

  /**
   * 清空历史数据
   * @returns 成功消息
   */
  @Post('clear')
  @ApiOperation({ summary: '清空历史数据' })
  @ApiResponse({ status: 200, description: '成功', type: Object })
  clearHistory(): { success: boolean; message: string } {
    this.monitoringService.clearHistory();
    return { success: true, message: '历史数据已清空' };
  }
}
