/**
 * 场景类
 * 表示3D场景，包含实体、光照和环境
 */
import * as THREE from 'three';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { Skybox } from './Skybox';
import { SceneGraph } from './SceneGraph';
import { SceneLayerManager } from './SceneLayerManager';

export class Scene extends EventEmitter {
  /** 场景ID */
  public id: string = '';

  /** 场景名称 */
  public name: string;

  /** Three.js场景 */
  private threeScene: THREE.Scene;

  /** 实体列表 */
  private entities: Entity[] = [];

  /** 天空盒 */
  private skybox: Skybox | null = null;

  /** 环境光 */
  private ambientLight: THREE.AmbientLight;

  /** 是否启用雾效 */
  private fogEnabled: boolean = false;

  /** 场景图 */
  private sceneGraph: SceneGraph;

  /** 场景图层管理器 */
  private layerManager: SceneLayerManager;

  /**
   * 创建场景实例
   * @param name 场景名称
   */
  constructor(name: string = '场景') {
    super();
    this.name = name;

    // 创建Three.js场景
    this.threeScene = new THREE.Scene();

    // 创建环境光
    this.ambientLight = new THREE.AmbientLight(0x404040, 1);
    this.threeScene.add(this.ambientLight);

    // 创建场景图
    this.sceneGraph = new SceneGraph(this);

    // 创建场景图层管理器
    this.layerManager = new SceneLayerManager(this, {
      createDefaultLayers: true,
      defaultLayerCount: 3
    });
  }

  /**
   * 更新场景
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新天空盒
    if (this.skybox) {
      this.skybox.update(deltaTime);
    }
  }

  /**
   * 固定时间步长更新
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   */
  public fixedUpdate(fixedDeltaTime: number): void {
    // 目前没有需要固定更新的内容
  }

  /**
   * 添加实体
   * @param entity 实体实例
   * @returns 添加的实体
   */
  public addEntity(entity: Entity): Entity {
    // 如果已经在场景中，则不做任何操作
    if (this.entities.includes(entity)) {
      return entity;
    }

    // 添加到实体列表
    this.entities.push(entity);

    // 获取实体的变换组件
    const transform = entity.getTransform();
    if (transform) {
      // 添加到Three.js场景
      const object3D = transform.getObject3D();
      this.threeScene.add(object3D);
    }

    // 发出实体添加事件
    this.emit('entityAdded', entity);

    return entity;
  }

  /**
   * 移除实体
   * @param entity 实体实例
   * @returns 是否成功移除
   */
  public removeEntity(entity: Entity): boolean {
    const index = this.entities.indexOf(entity);

    if (index === -1) {
      return false;
    }

    // 从实体列表中移除
    this.entities.splice(index, 1);

    // 获取实体的变换组件
    const transform = entity.getTransform();
    if (transform) {
      // 从Three.js场景中移除
      const object3D = transform.getObject3D();
      this.threeScene.remove(object3D);
    }

    // 发出实体移除事件
    this.emit('entityRemoved', entity);

    return true;
  }

  /**
   * 获取所有实体
   * @returns 实体数组
   */
  public getEntities(): Entity[] {
    return [...this.entities];
  }

  /**
   * 根据名称查找实体
   * @param name 实体名称
   * @returns 匹配的实体数组
   */
  public findEntitiesByName(name: string): Entity[] {
    return this.entities.filter(entity => entity.name === name);
  }

  /**
   * 根据标签查找实体
   * @param tag 实体标签
   * @returns 匹配的实体数组
   */
  public findEntitiesByTag(tag: string): Entity[] {
    return this.entities.filter(entity => entity.hasTag(tag));
  }

  /**
   * 设置天空盒
   * @param skybox 天空盒实例
   */
  public setSkybox(skybox: Skybox | null): void {
    // 移除旧的天空盒
    if (this.skybox) {
      const skyboxMesh = this.skybox.getMesh();
      if (skyboxMesh) {
        this.threeScene.remove(skyboxMesh);
      }
    }

    this.skybox = skybox;

    // 添加新的天空盒
    if (skybox) {
      const skyboxMesh = skybox.getMesh();
      if (skyboxMesh) {
        this.threeScene.add(skyboxMesh);
      }
    }
  }

  /**
   * 获取天空盒
   * @returns 天空盒实例
   */
  public getSkybox(): Skybox | null {
    return this.skybox;
  }

  /**
   * 设置环境光
   * @param color 颜色
   * @param intensity 强度
   */
  public setAmbientLight(color: THREE.ColorRepresentation, intensity: number): void {
    this.ambientLight.color.set(color);
    this.ambientLight.intensity = intensity;
  }

  /**
   * 获取环境光
   * @returns 环境光实例
   */
  public getAmbientLight(): THREE.AmbientLight {
    return this.ambientLight;
  }

  /**
   * 设置雾效
   * @param color 颜色
   * @param near 近距离
   * @param far 远距离
   */
  public setFog(color: THREE.ColorRepresentation, near: number, far: number): void {
    this.threeScene.fog = new THREE.Fog(color, near, far);
    this.fogEnabled = true;
  }

  /**
   * 设置指数雾效
   * @param color 颜色
   * @param density 密度
   */
  public setExponentialFog(color: THREE.ColorRepresentation, density: number): void {
    this.threeScene.fog = new THREE.FogExp2(color, density);
    this.fogEnabled = true;
  }

  /**
   * 清除雾效
   */
  public clearFog(): void {
    this.threeScene.fog = null;
    this.fogEnabled = false;
  }

  /**
   * 是否启用雾效
   * @returns 是否启用雾效
   */
  public isFogEnabled(): boolean {
    return this.fogEnabled;
  }

  /**
   * 获取Three.js场景
   * @returns Three.js场景实例
   */
  public getThreeScene(): THREE.Scene {
    return this.threeScene;
  }

  /**
   * 清空场景
   */
  public clear(): void {
    // 移除所有实体
    while (this.entities.length > 0) {
      this.removeEntity(this.entities[0]);
    }

    // 移除天空盒
    this.setSkybox(null);

    // 清除雾效
    this.clearFog();

    // 发出清空事件
    this.emit('cleared');
  }

  /**
   * 销毁场景
   */
  public dispose(): void {
    // 清空场景
    this.clear();

    // 移除环境光
    this.threeScene.remove(this.ambientLight);

    // 销毁场景图
    (this.sceneGraph as any).dispose();

    // 销毁场景图层管理器
    (this.layerManager as any).dispose();

    // 移除所有事件监听器
    this.removeAllListeners();
  }

  /**
   * 获取场景图
   * @returns 场景图实例
   */
  public getSceneGraph(): SceneGraph {
    return this.sceneGraph;
  }

  /**
   * 获取场景图层管理器
   * @returns 场景图层管理器实例
   */
  public getLayerManager(): SceneLayerManager {
    return this.layerManager;
  }

  /**
   * 获取根实体
   * @returns 根实体
   */
  public getRootEntity(): Entity | null {
    // 获取没有父实体的实体
    for (const entity of this.entities) {
      if (!entity.getParent()) {
        return entity;
      }
    }

    return null;
  }

  /**
   * 查找实体
   * @param predicate 查询函数
   * @returns 匹配的实体数组
   */
  public findEntities(predicate: (entity: Entity) => boolean): Entity[] {
    return this.entities.filter(predicate);
  }

  /**
   * 查找第一个实体
   * @param predicate 查询函数
   * @returns 匹配的实体，如果不存在则返回null
   */
  public findEntity(predicate: (entity: Entity) => boolean): Entity | null {
    return this.entities.find(predicate) || null;
  }

  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 实体实例，如果不存在则返回null
   */
  public findEntityById(id: string): Entity | null {
    return this.entities.find(entity => entity.id === id) || null;
  }

  /**
   * 是否可见
   * @returns 是否可见
   */
  public isVisible(): boolean {
    return true;
  }

  /**
   * 是否锁定
   * @returns 是否锁定
   */
  public isLocked(): boolean {
    return false;
  }

  /**
   * 获取场景ID
   * @returns 场景ID
   */
  public getId(): string {
    return this.id;
  }

  /**
   * 设置场景ID
   * @param id 场景ID
   */
  public setId(id: string): void {
    this.id = id;
  }

  /**
   * 获取场景名称
   * @returns 场景名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置场景名称
   * @param name 场景名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取活跃相机
   * @returns 活跃相机
   */
  public getActiveCamera(): any {
    // 查找场景中的相机实体
    for (const entity of this.entities) {
      const camera = entity.getComponent('Camera') as any;
      if (camera) {
        return camera;
      }
    }
    return null;
  }
}
