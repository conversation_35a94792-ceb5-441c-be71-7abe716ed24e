/**
 * 渲染服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class RenderService {
  private readonly logger = new Logger(RenderService.name);

  constructor(
    @Inject('RENDER_SERVICE') private readonly renderService: ClientProxy,
  ) {}

  /**
   * 创建渲染任务
   */
  async create(userId: string, createRenderJobDto: any) {
    try {
      return await firstValueFrom(
        this.renderService.send(
          { cmd: 'createRenderJob' },
          { userId, ...createRenderJobDto }
        )
      );
    } catch (error) {
      this.logger.error('创建渲染任务失败', error);
      throw error;
    }
  }

  /**
   * 获取所有渲染任务
   */
  async findAll(userId: string, status?: string, type?: string) {
    try {
      return await firstValueFrom(
        this.renderService.send(
          { cmd: 'findAllRenderJobs' },
          { userId, status, type }
        )
      );
    } catch (error) {
      this.logger.error('获取所有渲染任务失败', error);
      throw error;
    }
  }

  /**
   * 根据ID获取渲染任务
   */
  async findOne(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.renderService.send(
          { cmd: 'findRenderJobById' },
          { id, userId }
        )
      );
    } catch (error) {
      this.logger.error(`获取渲染任务ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 取消渲染任务
   */
  async cancel(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.renderService.send(
          { cmd: 'cancelRenderJob' },
          { id, userId }
        )
      );
    } catch (error) {
      this.logger.error(`取消渲染任务ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除渲染任务
   */
  async remove(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.renderService.send(
          { cmd: 'removeRenderJob' },
          { id, userId }
        )
      );
    } catch (error) {
      this.logger.error(`删除渲染任务ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 获取渲染结果
   */
  async getResult(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.renderService.send(
          { cmd: 'getRenderResult' },
          { id, userId }
        )
      );
    } catch (error) {
      this.logger.error(`获取渲染结果ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 获取渲染结果文件路径
   */
  async getResultFilePath(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.renderService.send(
          { cmd: 'getRenderResultFilePath' },
          { id, userId }
        )
      );
    } catch (error) {
      this.logger.error(`获取渲染结果ID ${id} 文件路径失败`, error);
      throw error;
    }
  }
}
