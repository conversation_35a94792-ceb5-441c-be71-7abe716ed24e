/**
 * 角色组件
 * 用于表示一个角色实体
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';

/**
 * 角色类型
 */
export enum AvatarType {
  /** 本地玩家 */
  LOCAL_PLAYER = 'local_player',
  /** 远程玩家 */
  REMOTE_PLAYER = 'remote_player',
  /** NPC */
  NPC = 'npc',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 角色组件配置
 */
export interface AvatarComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 角色类型 */
  type?: AvatarType;
  /** 用户ID */
  userId?: string;
  /** 角色名称 */
  name?: string;
  /** 角色模型URL */
  modelUrl?: string;
}

/**
 * 角色组件
 * 表示一个角色实体
 */
export class AvatarComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'AvatarComponent';



  /** 角色类型 */
  public avatarType: AvatarType;

  /** 用户ID */
  public userId: string;

  /** 角色名称 */
  public name: string;

  /** 角色模型URL */
  public modelUrl: string;

  /** 是否已加载 */
  public isLoaded: boolean = false;

  /** 是否正在加载 */
  public isLoading: boolean = false;

  /** 用户ID到角色实体的映射 */
  private static userAvatarEntities: Map<string, Entity> = new Map();

  /**
   * 构造函数
   * @param entity 实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: AvatarComponentConfig = {}) {
    super(AvatarComponent.TYPE);
    this.setEntity(entity);

    this.setEnabled(config.enabled !== undefined ? config.enabled : true);
    this.avatarType = config.type || AvatarType.OTHER;
    this.userId = config.userId || '';
    this.name = config.name || '';
    this.modelUrl = config.modelUrl || '';

    // 如果有用户ID，注册到映射中
    if (this.userId) {
      AvatarComponent.userAvatarEntities.set(this.userId, entity);
    }
  }

  /**
   * 获取用户的角色实体
   * @param userId 用户ID
   * @returns 角色实体
   */
  public static getUserAvatarEntity(userId: string): Entity | undefined {
    return AvatarComponent.userAvatarEntities.get(userId);
  }

  /**
   * 获取本地玩家的角色实体
   * @returns 本地玩家的角色实体
   */
  public static getSelfAvatarEntity(): Entity | undefined {
    for (const [, entity] of AvatarComponent.userAvatarEntities.entries()) {
      const avatarComponent = entity.getComponent<AvatarComponent>(AvatarComponent.TYPE);
      if (avatarComponent && avatarComponent.avatarType === AvatarType.LOCAL_PLAYER) {
        return entity;
      }
    }
    return undefined;
  }

  /**
   * 设置用户ID
   * @param userId 用户ID
   */
  public setUserId(userId: string): void {
    // 如果已有用户ID，从映射中移除
    if (this.userId) {
      AvatarComponent.userAvatarEntities.delete(this.userId);
    }

    // 设置新的用户ID
    this.userId = userId;

    // 如果有用户ID，注册到映射中
    if (this.userId) {
      AvatarComponent.userAvatarEntities.set(this.userId, this.entity);
    }
  }

  /**
   * 设置角色类型
   * @param type 角色类型
   */
  public setType(type: AvatarType): void {
    this.avatarType = type;
  }

  /**
   * 设置角色名称
   * @param name 角色名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 设置角色模型URL
   * @param modelUrl 角色模型URL
   */
  public setModelUrl(modelUrl: string): void {
    this.modelUrl = modelUrl;
  }

  /**
   * 设置加载状态
   * @param isLoaded 是否已加载
   */
  public setLoaded(isLoaded: boolean): void {
    this.isLoaded = isLoaded;
    this.isLoading = false;
  }

  /**
   * 设置正在加载状态
   * @param isLoading 是否正在加载
   */
  public setLoading(isLoading: boolean): void {
    this.isLoading = isLoading;
    if (!isLoading) {
      this.isLoaded = true;
    }
  }

  /**
   * 组件销毁时调用
   */
  public onDestroy(): void {
    // 如果有用户ID，从映射中移除
    if (this.userId) {
      AvatarComponent.userAvatarEntities.delete(this.userId);
    }
  }

  /**
   * 克隆组件
   * @param entity 目标实体
   * @returns 克隆的组件
   */
  public clone(entity: Entity): AvatarComponent {
    return new AvatarComponent(entity, {
      enabled: this.isEnabled(),
      type: this.avatarType,
      userId: this.userId,
      name: this.name,
      modelUrl: this.modelUrl
    });
  }
}
