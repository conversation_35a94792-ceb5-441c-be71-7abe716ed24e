/**
 * 事务监控服务
 */
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventBusService } from '../../event-bus/event-bus.service';
import {
  TRANSACTION_STARTED,
  TRANSACTION_PREPARED,
  TRANSACTION_COMMITTED,
  TRANSACTION_ABORTED,
  TRANSACTION_COMPLETED,
} from '../../event-bus/events';
import { Transaction, TransactionStatus, ParticipantStatus } from '../transaction.interface';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * 事务统计信息
 */
export interface TransactionStats {
  /** 总事务数 */
  totalTransactions: number;
  /** 成功事务数 */
  successfulTransactions: number;
  /** 失败事务数 */
  failedTransactions: number;
  /** 进行中事务数 */
  activeTransactions: number;
  /** 平均事务耗时（毫秒） */
  averageTransactionTime: number;
  /** 最长事务耗时（毫秒） */
  maxTransactionTime: number;
  /** 最短事务耗时（毫秒） */
  minTransactionTime: number;
  /** 按服务统计的事务数 */
  transactionsByService: Record<string, number>;
  /** 按状态统计的事务数 */
  transactionsByStatus: Record<string, number>;
  /** 按小时统计的事务数 */
  transactionsByHour: number[];
  /** 按天统计的事务数 */
  transactionsByDay: number[];
  /** 成功率 */
  successRate: number;
  /** 平均参与者数量 */
  averageParticipantCount: number;
  /** 最大参与者数量 */
  maxParticipantCount: number;
  /** 按参与者统计的事务数 */
  transactionsByParticipant: Record<string, number>;
  /** 按错误类型统计的事务数 */
  transactionsByErrorType: Record<string, number>;
  /** 统计时间范围（毫秒） */
  timeRange: number;
  /** 统计开始时间 */
  startTime: Date;
  /** 统计结束时间 */
  endTime: Date;
}

/**
 * 事务性能指标
 */
interface TransactionPerformanceMetrics {
  /** 事务ID */
  transactionId: string;
  /** 总耗时（毫秒） */
  totalDuration: number;
  /** 准备阶段耗时（毫秒） */
  prepareDuration: number;
  /** 提交阶段耗时（毫秒） */
  commitDuration: number;
  /** 参与者耗时（毫秒） */
  participantDurations: Record<string, number>;
  /** 网络延迟（毫秒） */
  networkLatency: number;
  /** 数据大小（字节） */
  dataSize: number;
  /** CPU使用率 */
  cpuUsage: number;
  /** 内存使用量（字节） */
  memoryUsage: number;
  /** 记录时间 */
  timestamp: Date;
}

/**
 * 事务监控服务
 */
@Injectable()
export class TransactionMonitorService implements OnModuleInit {
  private readonly logger = new Logger(TransactionMonitorService.name);
  private readonly transactions = new Map<string, Transaction>();
  private readonly transactionHistory = new Map<string, {
    transaction: Transaction;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    error?: string;
    participantCount: number;
  }>();
  private readonly performanceMetrics = new Map<string, TransactionPerformanceMetrics>();
  private readonly stats: TransactionStats = {
    totalTransactions: 0,
    successfulTransactions: 0,
    failedTransactions: 0,
    activeTransactions: 0,
    averageTransactionTime: 0,
    maxTransactionTime: 0,
    minTransactionTime: Number.MAX_SAFE_INTEGER,
    transactionsByService: {},
    transactionsByStatus: {},
    transactionsByHour: Array(24).fill(0),
    transactionsByDay: Array(30).fill(0),
    successRate: 0,
    averageParticipantCount: 0,
    maxParticipantCount: 0,
    transactionsByParticipant: {},
    transactionsByErrorType: {},
    timeRange: 24 * 60 * 60 * 1000, // 默认24小时
    startTime: new Date(),
    endTime: new Date(),
  };

  constructor(private readonly eventBusService: EventBusService) {
    this.subscribeToEvents();
  }

  /**
   * 模块初始化
   */
  onModuleInit() {
    this.logger.log('事务监控服务已初始化');

    // 初始化统计信息
    this.resetStats();
  }

  /**
   * 定时清理过期事务历史
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  cleanupTransactionHistory() {
    const now = Date.now();
    const threshold = now - this.stats.timeRange;
    let count = 0;

    for (const [transactionId, history] of this.transactionHistory.entries()) {
      if (history.startTime.getTime() < threshold) {
        this.transactionHistory.delete(transactionId);
        this.performanceMetrics.delete(transactionId);
        count++;
      }
    }

    this.logger.log(`已清理 ${count} 条过期事务历史记录`);
  }

  /**
   * 定时更新统计信息
   */
  @Cron(CronExpression.EVERY_HOUR)
  updateStats() {
    this.logger.debug('更新事务统计信息');

    // 更新时间范围
    this.stats.endTime = new Date();

    // 计算成功率
    const total = this.stats.successfulTransactions + this.stats.failedTransactions;
    this.stats.successRate = total > 0 ? this.stats.successfulTransactions / total : 0;

    // 更新按小时统计
    const hour = new Date().getHours();
    this.stats.transactionsByHour[hour] = this.getTransactionsInLastHour();

    // 更新按天统计
    const day = new Date().getDate() % 30;
    this.stats.transactionsByDay[day] = this.getTransactionsInLastDay();
  }

  /**
   * 订阅事务相关事件
   */
  private subscribeToEvents(): void {
    // 事务开始事件
    this.eventBusService.subscribe(TRANSACTION_STARTED, (event) => {
      const { transactionId, initiator, participants, startedAt, data } = event.data;

      // 记录事务
      this.transactions.set(transactionId, {
        id: transactionId,
        initiator,
        participants: {},
        status: TransactionStatus.STARTED,
        startTime: new Date(startedAt),
        data,
      });

      // 更新统计信息
      this.stats.totalTransactions++;
      this.stats.activeTransactions++;
      this.stats.transactionsByStatus[TransactionStatus.STARTED] =
        (this.stats.transactionsByStatus[TransactionStatus.STARTED] || 0) + 1;

      // 按服务统计
      this.stats.transactionsByService[initiator] =
        (this.stats.transactionsByService[initiator] || 0) + 1;

      // 按参与者统计
      const participantCount = participants ? participants.length : 0;
      if (participantCount > 0) {
        // 更新最大参与者数量
        this.stats.maxParticipantCount = Math.max(this.stats.maxParticipantCount, participantCount);

        // 更新平均参与者数量
        const totalTx = this.stats.totalTransactions;
        this.stats.averageParticipantCount =
          (this.stats.averageParticipantCount * (totalTx - 1) + participantCount) / totalTx;

        // 更新参与者统计
        for (const participant of participants) {
          this.stats.transactionsByParticipant[participant] =
            (this.stats.transactionsByParticipant[participant] || 0) + 1;
        }
      }

      // 记录事务历史
      this.transactionHistory.set(transactionId, {
        transaction: this.transactions.get(transactionId),
        startTime: new Date(startedAt),
        participantCount,
      });

      // 更新按小时统计
      const hour = new Date(startedAt).getHours();
      this.stats.transactionsByHour[hour]++;

      // 更新按天统计
      const day = new Date(startedAt).getDate() % 30;
      this.stats.transactionsByDay[day]++;

      this.logger.debug(`事务 ${transactionId} 已开始，发起者: ${initiator}，参与者数量: ${participantCount}`);
    });

    // 事务准备完成事件
    this.eventBusService.subscribe(TRANSACTION_PREPARED, (event) => {
      const { transactionId } = event.data;

      // 更新事务状态
      const transaction = this.transactions.get(transactionId);
      if (transaction) {
        transaction.status = TransactionStatus.PREPARED;

        // 更新统计信息
        this.stats.transactionsByStatus[TransactionStatus.PREPARED] =
          (this.stats.transactionsByStatus[TransactionStatus.PREPARED] || 0) + 1;

        this.logger.debug(`事务 ${transactionId} 已准备完成`);
      }
    });

    // 事务提交事件
    this.eventBusService.subscribe(TRANSACTION_COMMITTED, (event) => {
      const { transactionId } = event.data;

      // 更新事务状态
      const transaction = this.transactions.get(transactionId);
      if (transaction) {
        transaction.status = TransactionStatus.COMMITTED;

        // 更新统计信息
        this.stats.transactionsByStatus[TransactionStatus.COMMITTED] =
          (this.stats.transactionsByStatus[TransactionStatus.COMMITTED] || 0) + 1;

        this.logger.debug(`事务 ${transactionId} 已提交`);
      }
    });

    // 事务中止事件
    this.eventBusService.subscribe(TRANSACTION_ABORTED, (event) => {
      const { transactionId, reason } = event.data;

      // 更新事务状态
      const transaction = this.transactions.get(transactionId);
      if (transaction) {
        transaction.status = TransactionStatus.ABORTED;

        // 更新统计信息
        this.stats.transactionsByStatus[TransactionStatus.ABORTED] =
          (this.stats.transactionsByStatus[TransactionStatus.ABORTED] || 0) + 1;
        this.stats.failedTransactions++;
        this.stats.activeTransactions--;

        // 更新事务历史
        const history = this.transactionHistory.get(transactionId);
        if (history) {
          history.endTime = new Date();
          history.duration = history.endTime.getTime() - history.startTime.getTime();
          history.error = reason;

          // 更新统计信息
          this.updateTransactionTimeStats(history.duration);

          // 更新错误类型统计
          const errorType = this.categorizeError(reason);
          this.stats.transactionsByErrorType[errorType] =
            (this.stats.transactionsByErrorType[errorType] || 0) + 1;

          // 更新成功率
          const total = this.stats.successfulTransactions + this.stats.failedTransactions;
          this.stats.successRate = total > 0 ? this.stats.successfulTransactions / total : 0;
        }

        this.logger.debug(`事务 ${transactionId} 已中止，原因: ${reason}`);
      }
    });

    // 事务完成事件
    this.eventBusService.subscribe(TRANSACTION_COMPLETED, (event) => {
      const { transactionId, metrics } = event.data;

      // 更新事务状态
      const transaction = this.transactions.get(transactionId);
      if (transaction) {
        // 如果事务已提交，则计为成功
        if (transaction.status === TransactionStatus.COMMITTED) {
          this.stats.successfulTransactions++;

          // 更新成功率
          const total = this.stats.successfulTransactions + this.stats.failedTransactions;
          this.stats.successRate = total > 0 ? this.stats.successfulTransactions / total : 0;
        }

        // 更新活跃事务数
        this.stats.activeTransactions--;

        // 更新事务历史
        const history = this.transactionHistory.get(transactionId);
        if (history) {
          history.endTime = new Date();
          history.duration = history.endTime.getTime() - history.startTime.getTime();

          // 更新统计信息
          this.updateTransactionTimeStats(history.duration);

          // 记录性能指标
          if (metrics) {
            this.recordPerformanceMetrics(transactionId, {
              ...metrics,
              totalDuration: history.duration,
            });
          }
        }

        this.logger.debug(`事务 ${transactionId} 已完成，耗时: ${history?.duration || 0}ms`);
      }
    });
  }

  /**
   * 更新事务时间统计信息
   * @param duration 事务耗时（毫秒）
   */
  private updateTransactionTimeStats(duration: number): void {
    // 更新最大耗时
    if (duration > this.stats.maxTransactionTime) {
      this.stats.maxTransactionTime = duration;
    }

    // 更新最小耗时
    if (duration < this.stats.minTransactionTime) {
      this.stats.minTransactionTime = duration;
    }

    // 更新平均耗时
    const totalCompleted = this.stats.successfulTransactions + this.stats.failedTransactions;
    if (totalCompleted > 0) {
      const totalTime = this.stats.averageTransactionTime * (totalCompleted - 1) + duration;
      this.stats.averageTransactionTime = totalTime / totalCompleted;
    }
  }

  /**
   * 获取事务统计信息
   */
  getTransactionStats(): TransactionStats {
    // 更新统计结束时间
    this.stats.endTime = new Date();
    return { ...this.stats };
  }

  /**
   * 获取活跃事务列表
   */
  getActiveTransactions(): Transaction[] {
    return Array.from(this.transactions.values()).filter(
      transaction =>
        transaction.status !== TransactionStatus.COMMITTED &&
        transaction.status !== TransactionStatus.ABORTED
    );
  }

  /**
   * 获取事务历史
   * @param limit 限制数量
   * @param offset 偏移量
   * @param filters 过滤条件
   */
  getTransactionHistory(
    limit: number = 100,
    offset: number = 0,
    filters?: {
      status?: TransactionStatus;
      initiator?: string;
      participant?: string;
      startTimeFrom?: Date;
      startTimeTo?: Date;
      minDuration?: number;
      maxDuration?: number;
      errorType?: string;
    }
  ): {
    total: number;
    items: {
      transaction: Transaction;
      startTime: Date;
      endTime?: Date;
      duration?: number;
      error?: string;
      participantCount: number;
    }[];
  } {
    let history = Array.from(this.transactionHistory.values());

    // 应用过滤条件
    if (filters) {
      if (filters.status) {
        history = history.filter(h => h.transaction.status === filters.status);
      }

      if (filters.initiator) {
        history = history.filter(h => h.transaction.initiator === filters.initiator);
      }

      if (filters.participant) {
        history = history.filter(h =>
          Object.keys(h.transaction.participants || {}).includes(filters.participant)
        );
      }

      if (filters.startTimeFrom) {
        history = history.filter(h => h.startTime >= filters.startTimeFrom);
      }

      if (filters.startTimeTo) {
        history = history.filter(h => h.startTime <= filters.startTimeTo);
      }

      if (filters.minDuration) {
        history = history.filter(h => (h.duration || 0) >= filters.minDuration);
      }

      if (filters.maxDuration) {
        history = history.filter(h => (h.duration || 0) <= filters.maxDuration);
      }

      if (filters.errorType) {
        history = history.filter(h =>
          h.error && this.categorizeError(h.error) === filters.errorType
        );
      }
    }

    // 排序并分页
    history = history
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(offset, offset + limit);

    return {
      total: history.length,
      items: history,
    };
  }

  /**
   * 获取事务详情
   * @param transactionId 事务ID
   */
  getTransactionDetails(transactionId: string): {
    transaction: Transaction;
    startTime: Date;
    endTime?: Date;
    duration?: number;
    error?: string;
    participantCount: number;
    performanceMetrics?: TransactionPerformanceMetrics;
  } | null {
    const history = this.transactionHistory.get(transactionId);
    if (!history) {
      return null;
    }

    // 获取性能指标
    const metrics = this.performanceMetrics.get(transactionId);

    return {
      ...history,
      performanceMetrics: metrics,
    };
  }

  /**
   * 清理过期事务历史
   * @param maxAge 最大保留时间（毫秒）
   */
  cleanupTransactionHistory(maxAge: number = 24 * 60 * 60 * 1000): void {
    const now = Date.now();
    const threshold = now - maxAge;

    for (const [transactionId, history] of this.transactionHistory.entries()) {
      if (history.startTime.getTime() < threshold) {
        this.transactionHistory.delete(transactionId);
        this.performanceMetrics.delete(transactionId);
      }
    }

    this.logger.debug(`已清理过期事务历史，保留时间: ${maxAge}ms`);
  }

  /**
   * 重置统计信息
   */
  private resetStats(): void {
    const now = new Date();

    this.stats.startTime = new Date(now.getTime() - this.stats.timeRange);
    this.stats.endTime = now;
    this.stats.transactionsByHour = Array(24).fill(0);
    this.stats.transactionsByDay = Array(30).fill(0);
    this.stats.transactionsByService = {};
    this.stats.transactionsByStatus = {};
    this.stats.transactionsByParticipant = {};
    this.stats.transactionsByErrorType = {};
  }

  /**
   * 获取最近一小时的事务数量
   */
  private getTransactionsInLastHour(): number {
    const now = Date.now();
    const hourAgo = now - 60 * 60 * 1000;

    return Array.from(this.transactionHistory.values())
      .filter(history => history.startTime.getTime() >= hourAgo)
      .length;
  }

  /**
   * 获取最近一天的事务数量
   */
  private getTransactionsInLastDay(): number {
    const now = Date.now();
    const dayAgo = now - 24 * 60 * 60 * 1000;

    return Array.from(this.transactionHistory.values())
      .filter(history => history.startTime.getTime() >= dayAgo)
      .length;
  }

  /**
   * 记录事务性能指标
   * @param transactionId 事务ID
   * @param metrics 性能指标
   */
  recordPerformanceMetrics(transactionId: string, metrics: Partial<TransactionPerformanceMetrics>): void {
    const existingMetrics = this.performanceMetrics.get(transactionId) || {
      transactionId,
      totalDuration: 0,
      prepareDuration: 0,
      commitDuration: 0,
      participantDurations: {},
      networkLatency: 0,
      dataSize: 0,
      cpuUsage: 0,
      memoryUsage: 0,
      timestamp: new Date(),
    };

    // 更新指标
    this.performanceMetrics.set(transactionId, {
      ...existingMetrics,
      ...metrics,
      timestamp: new Date(),
    });
  }

  /**
   * 获取事务性能指标
   * @param transactionId 事务ID
   */
  getPerformanceMetrics(transactionId: string): TransactionPerformanceMetrics | null {
    return this.performanceMetrics.get(transactionId) || null;
  }

  /**
   * 获取所有事务性能指标
   */
  getAllPerformanceMetrics(): TransactionPerformanceMetrics[] {
    return Array.from(this.performanceMetrics.values());
  }

  /**
   * 对错误原因进行分类
   * @param reason 错误原因
   */
  private categorizeError(reason: string): string {
    if (!reason) {
      return '未知错误';
    }

    // 超时错误
    if (reason.includes('超时') || reason.includes('timeout')) {
      return '超时错误';
    }

    // 网络错误
    if (reason.includes('网络') || reason.includes('连接') || reason.includes('network') || reason.includes('connection')) {
      return '网络错误';
    }

    // 准备阶段错误
    if (reason.includes('准备') || reason.includes('prepare')) {
      return '准备阶段错误';
    }

    // 提交阶段错误
    if (reason.includes('提交') || reason.includes('commit')) {
      return '提交阶段错误';
    }

    // 回滚阶段错误
    if (reason.includes('回滚') || reason.includes('rollback')) {
      return '回滚阶段错误';
    }

    // 数据错误
    if (reason.includes('数据') || reason.includes('data')) {
      return '数据错误';
    }

    // 权限错误
    if (reason.includes('权限') || reason.includes('permission')) {
      return '权限错误';
    }

    // 默认分类
    return '其他错误';
  }
}
