import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from '@elastic/elasticsearch';
import { LogEntity, LogLevel } from './entities/log.entity';

@Injectable()
export class ElasticsearchLogService implements OnModuleInit {
  private readonly logger = new Logger(ElasticsearchLogService.name);
  private readonly client: Client;
  private readonly indexName: string;
  private readonly enabled: boolean;

  constructor(
    private readonly configService: ConfigService,
  ) {
    this.enabled = this.configService.get<boolean>('ELASTICSEARCH_ENABLED', false);
    this.indexName = this.configService.get<string>('ELASTICSEARCH_INDEX', 'logs');
    
    if (this.enabled) {
      const node = this.configService.get<string>('ELASTICSEARCH_NODE', 'http://localhost:9200');
      const username = this.configService.get<string>('ELASTICSEARCH_USERNAME');
      const password = this.configService.get<string>('ELASTICSEARCH_PASSWORD');
      
      const auth = username && password ? { username, password } : undefined;
      
      this.client = new Client({
        node,
        auth,
      });
    }
  }

  async onModuleInit() {
    if (this.enabled) {
      try {
        // 检查Elasticsearch连接
        await this.client.ping();
        this.logger.log('Elasticsearch连接成功');
        
        // 检查索引是否存在
        const indexExists = await this.client.indices.exists({
          index: this.indexName,
        });
        
        if (!indexExists) {
          // 创建索引
          await this.createIndex();
        }
      } catch (error) {
        this.logger.error(`Elasticsearch初始化失败: ${error.message}`, error.stack);
      }
    }
  }

  /**
   * 创建索引
   */
  private async createIndex(): Promise<void> {
    try {
      await this.client.indices.create({
        index: this.indexName,
        body: {
          mappings: {
            properties: {
              id: { type: 'keyword' },
              level: { type: 'keyword' },
              message: { type: 'text' },
              context: { type: 'keyword' },
              serviceId: { type: 'keyword' },
              serviceType: { type: 'keyword' },
              instanceId: { type: 'keyword' },
              hostname: { type: 'keyword' },
              stack: { type: 'text' },
              metadata: { type: 'object' },
              timestamp: { type: 'date' },
              createdAt: { type: 'date' },
            },
          },
          settings: {
            number_of_shards: 3,
            number_of_replicas: 1,
          },
        },
      });
      
      this.logger.log(`索引 ${this.indexName} 已创建`);
    } catch (error) {
      this.logger.error(`创建索引失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 检查是否启用Elasticsearch
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 索引单个日志
   */
  async indexLog(log: LogEntity): Promise<void> {
    if (!this.enabled) {
      return;
    }
    
    try {
      await this.client.index({
        index: this.indexName,
        id: log.id,
        body: log,
      });
    } catch (error) {
      this.logger.error(`索引日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量索引日志
   */
  async indexLogs(logs: LogEntity[]): Promise<void> {
    if (!this.enabled || logs.length === 0) {
      return;
    }
    
    try {
      const body = logs.flatMap(log => [
        { index: { _index: this.indexName, _id: log.id } },
        log,
      ]);
      
      await this.client.bulk({ body });
    } catch (error) {
      this.logger.error(`批量索引日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 搜索日志
   */
  async searchLogs(params: {
    query?: string;
    levels?: LogLevel[];
    serviceId?: string;
    serviceType?: string;
    instanceId?: string;
    hostname?: string;
    startTime?: Date;
    endTime?: Date;
    limit?: number;
    offset?: number;
    sortField?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<{ logs: LogEntity[]; total: number }> {
    if (!this.enabled) {
      return { logs: [], total: 0 };
    }
    
    try {
      // 构建查询
      const must: any[] = [];
      
      if (params.query) {
        must.push({
          multi_match: {
            query: params.query,
            fields: ['message', 'context', 'stack'],
          },
        });
      }
      
      if (params.levels && params.levels.length > 0) {
        must.push({
          terms: {
            level: params.levels,
          },
        });
      }
      
      if (params.serviceId) {
        must.push({
          term: {
            serviceId: params.serviceId,
          },
        });
      }
      
      if (params.serviceType) {
        must.push({
          term: {
            serviceType: params.serviceType,
          },
        });
      }
      
      if (params.instanceId) {
        must.push({
          term: {
            instanceId: params.instanceId,
          },
        });
      }
      
      if (params.hostname) {
        must.push({
          term: {
            hostname: params.hostname,
          },
        });
      }
      
      // 时间范围
      if (params.startTime || params.endTime) {
        const range: any = {};
        
        if (params.startTime) {
          range.gte = params.startTime.toISOString();
        }
        
        if (params.endTime) {
          range.lte = params.endTime.toISOString();
        }
        
        must.push({
          range: {
            timestamp: range,
          },
        });
      }
      
      // 执行查询
      const response = await this.client.search({
        index: this.indexName,
        body: {
          query: {
            bool: {
              must,
            },
          },
          sort: [
            {
              [params.sortField || 'timestamp']: {
                order: params.sortOrder?.toLowerCase() || 'desc',
              },
            },
          ],
          from: params.offset || 0,
          size: params.limit || 100,
        },
      });
      
      // 解析结果
      const logs = response.hits.hits.map(hit => hit._source as LogEntity);
      const total = response.hits.total.value;
      
      return { logs, total };
    } catch (error) {
      this.logger.error(`搜索日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取日志统计信息
   */
  async getLogStats(params: {
    startTime?: Date;
    endTime?: Date;
    serviceType?: string;
  }): Promise<{
    total: number;
    byLevel: Record<LogLevel, number>;
    byService: Record<string, number>;
    byTime: { timestamp: Date; count: number }[];
  }> {
    if (!this.enabled) {
      return {
        total: 0,
        byLevel: {
          [LogLevel.DEBUG]: 0,
          [LogLevel.INFO]: 0,
          [LogLevel.WARN]: 0,
          [LogLevel.ERROR]: 0,
          [LogLevel.FATAL]: 0,
        },
        byService: {},
        byTime: [],
      };
    }
    
    try {
      // 构建查询
      const must: any[] = [];
      
      if (params.serviceType) {
        must.push({
          term: {
            serviceType: params.serviceType,
          },
        });
      }
      
      // 时间范围
      if (params.startTime || params.endTime) {
        const range: any = {};
        
        if (params.startTime) {
          range.gte = params.startTime.toISOString();
        }
        
        if (params.endTime) {
          range.lte = params.endTime.toISOString();
        }
        
        must.push({
          range: {
            timestamp: range,
          },
        });
      }
      
      // 执行查询
      const response = await this.client.search({
        index: this.indexName,
        body: {
          query: {
            bool: {
              must,
            },
          },
          size: 0,
          aggs: {
            total_count: {
              value_count: {
                field: 'id',
              },
            },
            by_level: {
              terms: {
                field: 'level',
                size: 10,
              },
            },
            by_service: {
              terms: {
                field: 'serviceType',
                size: 20,
              },
            },
            by_time: {
              date_histogram: {
                field: 'timestamp',
                calendar_interval: 'hour',
                format: 'yyyy-MM-dd HH:mm:ss',
              },
            },
          },
        },
      });
      
      // 解析结果
      const total = response.aggregations.total_count.value;
      
      const byLevel: Record<LogLevel, number> = {
        [LogLevel.DEBUG]: 0,
        [LogLevel.INFO]: 0,
        [LogLevel.WARN]: 0,
        [LogLevel.ERROR]: 0,
        [LogLevel.FATAL]: 0,
      };
      
      for (const bucket of response.aggregations.by_level.buckets) {
        byLevel[bucket.key] = bucket.doc_count;
      }
      
      const byService: Record<string, number> = {};
      
      for (const bucket of response.aggregations.by_service.buckets) {
        byService[bucket.key || 'unknown'] = bucket.doc_count;
      }
      
      const byTime = response.aggregations.by_time.buckets.map(bucket => ({
        timestamp: new Date(bucket.key_as_string),
        count: bucket.doc_count,
      }));
      
      return { total, byLevel, byService, byTime };
    } catch (error) {
      this.logger.error(`获取日志统计信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
