/**
 * 基础负载均衡策略
 */
import { Logger } from '@nestjs/common';
import { 
  LoadBalancerStrategy, 
  LoadBalancerConfig, 
  LoadBalancerContext,
  LoadBalancerAlgorithm
} from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

/**
 * 基础负载均衡策略
 */
export abstract class BaseLoadBalancerStrategy implements LoadBalancerStrategy {
  protected readonly logger = new Logger(this.constructor.name);
  protected config: LoadBalancerConfig;
  
  /**
   * 构造函数
   * @param name 策略名称
   */
  constructor(protected readonly name: string) {
    this.config = {
      algorithm: LoadBalancerAlgorithm.RANDOM,
      enableStickySession: false,
      stickySessionTimeout: 30 * 60 * 1000, // 30分钟
      enableFailover: true,
      failoverRetries: 3,
      enableZoneAffinity: false,
      zoneAffinityWeight: 0.8,
      options: {},
    };
  }
  
  /**
   * 选择服务实例
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  async select(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null> {
    if (!instances || instances.length === 0) {
      return null;
    }
    
    // 如果只有一个实例，直接返回
    if (instances.length === 1) {
      return instances[0];
    }
    
    // 处理粘性会话
    if (this.config.enableStickySession && context.sessionId) {
      const stickyInstance = this.handleStickySession(instances, context);
      if (stickyInstance) {
        return stickyInstance;
      }
    }
    
    // 处理区域亲和性
    if (this.config.enableZoneAffinity && context.clientZone) {
      const zoneInstances = this.handleZoneAffinity(instances, context);
      if (zoneInstances.length > 0) {
        return this.doSelect(zoneInstances, context);
      }
    }
    
    // 执行具体的选择逻辑
    return this.doSelect(instances, context);
  }
  
  /**
   * 执行具体的选择逻辑
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected abstract doSelect(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null>;
  
  /**
   * 初始化策略
   * @param config 负载均衡配置
   */
  init(config: LoadBalancerConfig): void {
    this.config = {
      ...this.config,
      ...config,
    };
    
    this.logger.log(`已初始化负载均衡策略: ${this.name}`);
  }
  
  /**
   * 更新策略配置
   * @param config 负载均衡配置
   */
  updateConfig(config: LoadBalancerConfig): void {
    this.config = {
      ...this.config,
      ...config,
    };
    
    this.logger.log(`已更新负载均衡策略配置: ${this.name}`);
  }
  
  /**
   * 获取策略名称
   */
  getName(): string {
    return this.name;
  }
  
  /**
   * 获取策略配置
   */
  getConfig(): LoadBalancerConfig {
    return this.config;
  }
  
  /**
   * 重置策略状态
   */
  reset(): void {
    // 子类可以重写此方法
  }
  
  /**
   * 处理粘性会话
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected handleStickySession(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): ServiceInstanceEntity | null {
    if (!context.sessionId) {
      return null;
    }
    
    // 这里简单实现，实际应该使用缓存或存储来保存会话与实例的映射
    // 使用会话ID的哈希值来选择实例
    const sessionHash = this.hashCode(context.sessionId);
    const index = sessionHash % instances.length;
    
    return instances[index];
  }
  
  /**
   * 处理区域亲和性
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected handleZoneAffinity(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): ServiceInstanceEntity[] {
    if (!context.clientZone) {
      return instances;
    }
    
    // 筛选出与客户端同区域的实例
    const sameZoneInstances = instances.filter(
      instance => instance.zone === context.clientZone,
    );
    
    // 如果同区域实例数量足够，则返回同区域实例
    if (sameZoneInstances.length > 0) {
      return sameZoneInstances;
    }
    
    // 否则返回所有实例
    return instances;
  }
  
  /**
   * 计算字符串的哈希值
   * @param str 字符串
   */
  protected hashCode(str: string): number {
    let hash = 0;
    if (str.length === 0) {
      return hash;
    }
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    return Math.abs(hash);
  }
}
