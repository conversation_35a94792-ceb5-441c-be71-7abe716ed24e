import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AgonesService } from './agones.service';
import { AgonesController } from './agones.controller';

@Module({
  imports: [
    ConfigModule,
    EventEmitterModule.forRoot(),
  ],
  controllers: [AgonesController],
  providers: [AgonesService],
  exports: [AgonesService],
})
export class AgonesModule {}
