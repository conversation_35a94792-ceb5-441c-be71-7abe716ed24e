/**
 * 性能演示控制器
 * 用于演示缓存、压缩和批处理功能
 */
import { Controller, Get, Post, Body, Query, Headers, Res, Inject } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { CacheService } from '../../../../shared/cache';
import { MessageCompressorService } from '../../../../shared/compression';
import { BatchProcessorService } from '../../../../shared/batch';
import { Logger } from '@nestjs/common';

@ApiTags('性能演示')
@Controller('performance-demo')
export class PerformanceDemoController {
  private readonly logger = new Logger(PerformanceDemoController.name);
  private readonly batchProcessor: BatchProcessorService<any, any>;

  constructor(
    private readonly cacheService: CacheService,
    private readonly messageCompressorService: MessageCompressorService,
  ) {
    // 创建批处理器
    this.batchProcessor = new BatchProcessorService<any, any>(
      async (inputs, options) => {
        // 模拟批处理
        await new Promise(resolve => setTimeout(resolve, 10));
        return inputs.map(input => ({ ...input, processed: true }));
      },
      {
        maxBatchSize: 50,
        maxWaitTime: 50,
        enableStats: true,
      },
    );
  }

  /**
   * 缓存测试
   * 演示缓存功能
   */
  @Get('cache-test')
  @ApiOperation({ summary: '缓存测试' })
  @ApiResponse({ status: 200, description: '成功' })
  async cacheTest(@Query('key') key: string, @Res() res: Response) {
    const cacheKey = `cache-test:${key || 'default'}`;
    
    // 使用缓存服务
    const data = await this.cacheService.get(cacheKey, async () => {
      // 模拟耗时操作
      await new Promise(resolve => setTimeout(resolve, 100));
      return {
        key,
        timestamp: Date.now(),
        value: Math.random(),
      };
    }, 60000); // 缓存60秒
    
    // 获取缓存统计
    const stats = this.cacheService.getStats();
    
    // 设置缓存头
    res.setHeader('X-Cache', stats.hits > 0 ? 'HIT' : 'MISS');
    res.setHeader('X-Cache-Hits', stats.hits);
    res.setHeader('X-Cache-Misses', stats.misses);
    res.setHeader('X-Cache-Hit-Rate', stats.hitRate.toFixed(2));
    
    return res.json({
      data,
      cacheStats: {
        hits: stats.hits,
        misses: stats.misses,
        hitRate: stats.hitRate,
        size: stats.size,
      },
    });
  }

  /**
   * 压缩测试
   * 演示消息压缩功能
   */
  @Post('compression-test')
  @ApiOperation({ summary: '压缩测试' })
  @ApiResponse({ status: 200, description: '成功' })
  async compressionTest(
    @Body() message: any,
    @Headers('accept-encoding') acceptEncoding: string,
    @Res() res: Response,
  ) {
    // 压缩消息
    const startTime = Date.now();
    const compressed = await this.messageCompressorService.compress(message);
    const compressionTime = Date.now() - startTime;
    
    // 获取压缩统计
    const stats = this.messageCompressorService.getStats();
    
    // 计算压缩比率
    const originalSize = Buffer.byteLength(JSON.stringify(message), 'utf-8');
    const compressedSize = compressed.data.length;
    const compressionRatio = compressedSize / originalSize;
    
    // 设置压缩头
    res.setHeader('X-Compression-Algorithm', compressed.algorithm);
    res.setHeader('X-Compression-Ratio', compressionRatio.toFixed(2));
    res.setHeader('X-Compression-Time', compressionTime);
    res.setHeader('X-Original-Size', originalSize);
    res.setHeader('X-Compressed-Size', compressedSize);
    
    return res.json({
      message: '压缩测试成功',
      compressionStats: {
        algorithm: compressed.algorithm,
        originalSize,
        compressedSize,
        compressionRatio,
        compressionTime,
        totalCompressed: stats.compressedCount,
        totalUncompressed: stats.uncompressedCount,
        avgCompressionRatio: stats.compressionRatio,
      },
    });
  }

  /**
   * 批处理测试
   * 演示批处理功能
   */
  @Post('batch-test')
  @ApiOperation({ summary: '批处理测试' })
  @ApiResponse({ status: 200, description: '成功' })
  async batchTest(@Body() item: any, @Res() res: Response) {
    // 使用批处理器
    const startTime = Date.now();
    const result = await this.batchProcessor.process(item);
    const processingTime = Date.now() - startTime;
    
    // 获取批处理统计
    const stats = this.batchProcessor.getStats();
    
    // 设置批处理头
    res.setHeader('X-Batch-Size', stats.avgBatchSize.toFixed(2));
    res.setHeader('X-Batch-Time', stats.avgBatchTime.toFixed(2));
    res.setHeader('X-Processing-Time', processingTime);
    
    return res.json({
      result,
      batchStats: {
        batchCount: stats.batchCount,
        itemCount: stats.itemCount,
        avgBatchSize: stats.avgBatchSize,
        avgBatchTime: stats.avgBatchTime,
        avgWaitTime: stats.avgWaitTime,
        queueSize: stats.queueSize,
      },
    });
  }

  /**
   * 性能统计
   * 获取所有性能统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '性能统计' })
  @ApiResponse({ status: 200, description: '成功' })
  async getStats() {
    return {
      cache: this.cacheService.getStats(),
      compression: this.messageCompressorService.getStats(),
      batch: this.batchProcessor.getStats(),
    };
  }

  /**
   * 重置统计
   * 重置所有性能统计信息
   */
  @Post('reset-stats')
  @ApiOperation({ summary: '重置统计' })
  @ApiResponse({ status: 200, description: '成功' })
  async resetStats() {
    this.cacheService.resetStats();
    this.messageCompressorService.resetStats();
    this.batchProcessor.resetStats();
    
    return { message: '已重置所有统计信息' };
  }
}
