/**
 * 项目控制器
 */
import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { MessagePattern } from '@nestjs/microservices';
import { ProjectsService } from './projects.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { AddProjectMemberDto } from './dto/add-project-member.dto';
import { UpdateProjectMemberDto } from './dto/update-project-member.dto';
import { CreateProjectSettingDto } from './dto/create-project-setting.dto';
import { Project } from './entities/project.entity';
import { ProjectMember } from './entities/project-member.entity';
import { ProjectSetting } from './entities/project-setting.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('项目')
@Controller('projects')
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建项目' })
  @ApiResponse({ status: 201, description: '项目创建成功', type: Project })
  async create(@Request() req, @Body() createProjectDto: CreateProjectDto): Promise<Project> {
    return this.projectsService.create(req.user.id, createProjectDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有项目' })
  @ApiResponse({ status: 200, description: '返回所有项目', type: [Project] })
  async findAll(@Request() req): Promise<Project[]> {
    return this.projectsService.findAll(req.user.id);
  }

  @Get('my')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取我的项目' })
  @ApiResponse({ status: 200, description: '返回用户拥有的项目', type: [Project] })
  async findMyProjects(@Request() req): Promise<Project[]> {
    return this.projectsService.findUserProjects(req.user.id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取项目' })
  @ApiResponse({ status: 200, description: '返回项目信息', type: Project })
  async findOne(@Param('id') id: string): Promise<Project> {
    return this.projectsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新项目' })
  @ApiResponse({ status: 200, description: '项目更新成功', type: Project })
  async update(
    @Param('id') id: string,
    @Request() req,
    @Body() updateProjectDto: UpdateProjectDto,
  ): Promise<Project> {
    return this.projectsService.update(id, req.user.id, updateProjectDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除项目' })
  @ApiResponse({ status: 204, description: '项目删除成功' })
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.projectsService.remove(id, req.user.id);
  }

  @Post(':id/members')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '添加项目成员' })
  @ApiResponse({ status: 201, description: '项目成员添加成功', type: ProjectMember })
  async addMember(
    @Param('id') id: string,
    @Request() req,
    @Body() addMemberDto: AddProjectMemberDto,
  ): Promise<ProjectMember> {
    return this.projectsService.addMember(id, req.user.id, addMemberDto);
  }

  @Patch(':id/members/:memberId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新项目成员' })
  @ApiResponse({ status: 200, description: '项目成员更新成功', type: ProjectMember })
  async updateMember(
    @Param('id') id: string,
    @Param('memberId') memberId: string,
    @Request() req,
    @Body() updateMemberDto: UpdateProjectMemberDto,
  ): Promise<ProjectMember> {
    return this.projectsService.updateMember(id, memberId, req.user.id, updateMemberDto);
  }

  @Delete(':id/members/:memberId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除项目成员' })
  @ApiResponse({ status: 204, description: '项目成员删除成功' })
  async removeMember(
    @Param('id') id: string,
    @Param('memberId') memberId: string,
    @Request() req,
  ): Promise<void> {
    return this.projectsService.removeMember(id, memberId, req.user.id);
  }

  @Post(':id/settings')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建项目设置' })
  @ApiResponse({ status: 201, description: '项目设置创建成功', type: ProjectSetting })
  async createSetting(
    @Param('id') id: string,
    @Body() createSettingDto: CreateProjectSettingDto,
  ): Promise<ProjectSetting> {
    return this.projectsService.createSetting(id, createSettingDto);
  }

  @Get(':id/settings/:key')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取项目设置' })
  @ApiResponse({ status: 200, description: '返回项目设置', type: ProjectSetting })
  async getSetting(@Param('id') id: string, @Param('key') key: string): Promise<ProjectSetting> {
    return this.projectsService.getSetting(id, key);
  }

  @Delete(':id/settings/:key')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除项目设置' })
  @ApiResponse({ status: 204, description: '项目设置删除成功' })
  async removeSetting(@Param('id') id: string, @Param('key') key: string): Promise<void> {
    return this.projectsService.removeSetting(id, key);
  }

  // 微服务消息处理
  @MessagePattern({ cmd: 'findProjectById' })
  async handleFindProjectById(id: string): Promise<Project> {
    return this.projectsService.findOne(id);
  }

  @MessagePattern({ cmd: 'checkProjectPermission' })
  async handleCheckProjectPermission(data: { projectId: string; userId: string; roles: string[] }): Promise<boolean> {
    return this.projectsService.checkPermission(data.projectId, data.userId, data.roles as any[]);
  }
}
