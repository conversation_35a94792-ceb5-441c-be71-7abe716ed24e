/**
 * 渲染控制器
 */
import { Controller, Get, Post, Body, Param, Delete, UseGuards, Request, Query, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { RenderService } from './render.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('渲染')
@Controller('render')
export class RenderController {
  constructor(private readonly renderService: RenderService) {}

  @Post('jobs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建渲染任务' })
  @ApiResponse({ status: 201, description: '渲染任务创建成功' })
  async create(@Request() req, @Body() createRenderJobDto: any) {
    return this.renderService.create(req.user.id, createRenderJobDto);
  }

  @Get('jobs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有渲染任务' })
  @ApiResponse({ status: 200, description: '返回所有渲染任务' })
  async findAll(
    @Request() req,
    @Query('status') status?: string,
    @Query('type') type?: string,
  ) {
    return this.renderService.findAll(req.user.id, status, type);
  }

  @Get('jobs/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取渲染任务' })
  @ApiResponse({ status: 200, description: '返回渲染任务信息' })
  async findOne(@Param('id') id: string, @Request() req) {
    return this.renderService.findOne(id, req.user.id);
  }

  @Post('jobs/:id/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '取消渲染任务' })
  @ApiResponse({ status: 200, description: '渲染任务取消成功' })
  async cancel(@Param('id') id: string, @Request() req) {
    return this.renderService.cancel(id, req.user.id);
  }

  @Delete('jobs/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除渲染任务' })
  @ApiResponse({ status: 204, description: '渲染任务删除成功' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.renderService.remove(id, req.user.id);
  }

  @Get('results/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取渲染结果' })
  @ApiResponse({ status: 200, description: '返回渲染结果信息' })
  async getResult(@Param('id') id: string, @Request() req) {
    return this.renderService.getResult(id, req.user.id);
  }

  @Get('results/:id/download')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '下载渲染结果文件' })
  @ApiResponse({ status: 200, description: '返回渲染结果文件' })
  async downloadResult(@Param('id') id: string, @Request() req, @Res() res: Response) {
    const filePath = await this.renderService.getResultFilePath(id, req.user.id);
    return res.download(filePath);
  }
}
