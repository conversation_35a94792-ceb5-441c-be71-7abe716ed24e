/**
 * 事务协调器服务
 */
import { Injectable, Logger, OnModuleInit, Inject } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import { EventBusService } from '../event-bus/event-bus.service';
import {
  TRANSACTION_ABORTED,
  TRANSACTION_COMMITTED,
  TRANSACTION_COMPLETED,
  TRANSACTION_PREPARED,
  TRANSACTION_STARTED,
} from '../event-bus/events';
import {
  Transaction,
  TransactionOptions,
  TransactionParticipant,
  TransactionParticipantHandler,
  TransactionResult,
  TransactionStatus,
  ParticipantStatus,
} from './transaction.interface';
import { DEFAULT_TRANSACTION_TIMEOUT, TRANSACTION_LOG_FILENAME, TRANSACTION_OPTIONS } from './transaction.constants';

@Injectable()
export class TransactionCoordinatorService implements OnModuleInit {
  private readonly logger = new Logger(TransactionCoordinatorService.name);
  private readonly transactions = new Map<string, Transaction>();
  private readonly participantHandlers = new Map<string, TransactionParticipantHandler>();
  private readonly logFilePath: string;

  constructor(
    @Inject(TRANSACTION_OPTIONS) private readonly options: TransactionOptions,
    private readonly eventBusService: EventBusService,
  ) {
    this.logFilePath = options.logStoragePath
      ? path.join(options.logStoragePath, TRANSACTION_LOG_FILENAME)
      : path.join(process.cwd(), 'logs', TRANSACTION_LOG_FILENAME);
  }

  /**
   * 模块初始化
   */
  async onModuleInit() {
    // 确保日志目录存在
    if (this.options.enableLogging) {
      const logDir = path.dirname(this.logFilePath);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }

      // 恢复未完成的事务
      await this.recoverTransactions();
    }

    // 订阅事务相关事件
    this.subscribeToEvents();

    this.logger.log(`事务协调器已初始化，服务名称: ${this.options.serviceName}`);
  }

  /**
   * 订阅事务相关事件
   */
  private subscribeToEvents() {
    // 事务准备事件
    this.eventBusService.subscribe(TRANSACTION_PREPARED, async (event) => {
      const { transactionId, participantId, prepared } = event.data;

      // 只处理当前服务作为协调者的事务
      const transaction = this.transactions.get(transactionId);
      if (!transaction || transaction.initiator !== this.options.serviceName) {
        return;
      }

      if (prepared) {
        await this.handleParticipantPrepared(transactionId, participantId);
      } else {
        await this.abortTransaction(transactionId, `参与者 ${participantId} 准备失败`);
      }
    });
  }

  /**
   * 恢复未完成的事务
   */
  private async recoverTransactions() {
    if (!this.options.enableLogging || !fs.existsSync(this.logFilePath)) {
      return;
    }

    try {
      const logContent = fs.readFileSync(this.logFilePath, 'utf-8');
      const logLines = logContent.split('\n').filter(line => line.trim());

      for (const line of logLines) {
        try {
          const logEntry = JSON.parse(line);

          if (logEntry.type === 'transaction' && logEntry.status !== TransactionStatus.COMMITTED && logEntry.status !== TransactionStatus.ABORTED) {
            const transaction = logEntry.transaction as Transaction;

            // 检查事务是否已超时
            const now = new Date();
            const elapsedTime = now.getTime() - new Date(transaction.startTime).getTime();

            if (elapsedTime > transaction.timeout) {
              // 事务已超时，执行回滚
              this.logger.warn(`恢复超时事务 ${transaction.id}，执行回滚`);
              await this.abortTransaction(transaction.id, '事务恢复时已超时');
            } else {
              // 将事务添加到内存中
              this.transactions.set(transaction.id, transaction);

              // 根据事务状态继续执行
              switch (transaction.status) {
                case TransactionStatus.STARTED:
                  // 重新发送准备请求
                  await this.prepareTransaction(transaction.id);
                  break;
                case TransactionStatus.PREPARED:
                  // 重新发送提交请求
                  await this.commitTransaction(transaction.id);
                  break;
                case TransactionStatus.PREPARING:
                case TransactionStatus.COMMITTING:
                case TransactionStatus.ABORTING:
                  // 这些状态需要重新检查参与者状态
                  await this.checkTransactionStatus(transaction.id);
                  break;
              }
            }
          }
        } catch (error) {
          this.logger.error(`解析事务日志条目失败: ${error.message}`, error.stack);
        }
      }
    } catch (error) {
      this.logger.error(`恢复事务失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 检查事务状态
   * @param transactionId 事务ID
   */
  private async checkTransactionStatus(transactionId: string) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      return;
    }

    // 检查所有参与者的状态
    const allPrepared = Object.values(transaction.participants).every(
      p => p.status === ParticipantStatus.PREPARED
    );

    if (allPrepared && transaction.status === TransactionStatus.PREPARING) {
      // 所有参与者都已准备好，可以提交
      await this.commitTransaction(transactionId);
    }
  }

  /**
   * 注册事务参与者处理器
   * @param participantId 参与者ID
   * @param handler 处理器
   */
  registerParticipantHandler(participantId: string, handler: TransactionParticipantHandler) {
    this.participantHandlers.set(participantId, handler);
    this.logger.debug(`已注册事务参与者处理器: ${participantId}`);
  }

  /**
   * 开始新事务
   * @param participants 参与者ID列表
   * @param data 事务数据
   * @param timeout 超时时间（毫秒）
   */
  async startTransaction(
    participants: string[],
    data?: any,
    timeout?: number,
  ): Promise<TransactionResult> {
    const transactionId = uuidv4();
    const now = new Date();

    // 创建事务对象
    const transaction: Transaction = {
      id: transactionId,
      status: TransactionStatus.STARTED,
      initiator: this.options.serviceName,
      participants: {},
      startTime: now,
      timeout: timeout || this.options.defaultTimeout || DEFAULT_TRANSACTION_TIMEOUT,
      data,
    };

    // 添加参与者
    for (const participantId of participants) {
      transaction.participants[participantId] = {
        id: participantId,
        status: ParticipantStatus.JOINED,
        lastUpdated: now,
      };
    }

    // 保存事务
    this.transactions.set(transactionId, transaction);

    // 记录事务日志
    await this.logTransaction(transaction);

    // 发布事务开始事件
    await this.eventBusService.publish(TRANSACTION_STARTED, {
      transactionId,
      initiator: this.options.serviceName,
      participants,
      startedAt: now,
    });

    // 开始准备阶段
    await this.prepareTransaction(transactionId);

    return {
      success: true,
      transactionId,
    };
  }

  /**
   * 准备事务
   * @param transactionId 事务ID
   */
  private async prepareTransaction(transactionId: string) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      return {
        success: false,
        error: `事务 ${transactionId} 不存在`,
        transactionId,
      };
    }

    // 更新事务状态
    transaction.status = TransactionStatus.PREPARING;
    await this.logTransaction(transaction);

    // 向所有参与者发送准备请求
    for (const participantId of Object.keys(transaction.participants)) {
      const participant = transaction.participants[participantId];
      participant.status = ParticipantStatus.PREPARING;
      participant.lastUpdated = new Date();

      // 如果是本地参与者，直接调用处理器
      const handler = this.participantHandlers.get(participantId);
      if (handler) {
        try {
          const prepared = await handler.prepare(transactionId, transaction.data);
          if (prepared) {
            participant.status = ParticipantStatus.PREPARED;
            participant.lastUpdated = new Date();
          } else {
            // 准备失败，中止事务
            await this.abortTransaction(transactionId, `参与者 ${participantId} 准备失败`);
            return;
          }
        } catch (error) {
          this.logger.error(`参与者 ${participantId} 准备阶段异常: ${error.message}`, error.stack);
          await this.abortTransaction(transactionId, `参与者 ${participantId} 准备阶段异常: ${error.message}`);
          return;
        }
      } else {
        // 远程参与者，通过事件总线发送准备请求
        await this.eventBusService.publish(`${participantId}.prepare`, {
          transactionId,
          data: transaction.data,
        });
      }
    }

    // 检查是否所有参与者都已准备好
    await this.checkAllParticipantsPrepared(transactionId);
  }

  /**
   * 处理参与者准备完成
   * @param transactionId 事务ID
   * @param participantId 参与者ID
   */
  private async handleParticipantPrepared(transactionId: string, participantId: string) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      return;
    }

    const participant = transaction.participants[participantId];
    if (!participant) {
      return;
    }

    // 更新参与者状态
    participant.status = ParticipantStatus.PREPARED;
    participant.lastUpdated = new Date();
    await this.logTransaction(transaction);

    // 检查是否所有参与者都已准备好
    await this.checkAllParticipantsPrepared(transactionId);
  }

  /**
   * 检查是否所有参与者都已准备好
   * @param transactionId 事务ID
   */
  private async checkAllParticipantsPrepared(transactionId: string) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      return;
    }

    // 检查是否所有参与者都已准备好
    const allPrepared = Object.values(transaction.participants).every(
      p => p.status === ParticipantStatus.PREPARED
    );

    if (allPrepared) {
      // 所有参与者都已准备好，更新事务状态
      transaction.status = TransactionStatus.PREPARED;
      await this.logTransaction(transaction);

      // 开始提交阶段
      await this.commitTransaction(transactionId);
    }
  }

  /**
   * 提交事务
   * @param transactionId 事务ID
   */
  private async commitTransaction(transactionId: string) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      return {
        success: false,
        error: `事务 ${transactionId} 不存在`,
        transactionId,
      };
    }

    // 更新事务状态
    transaction.status = TransactionStatus.COMMITTING;
    await this.logTransaction(transaction);

    // 发布事务提交事件
    await this.eventBusService.publish(TRANSACTION_COMMITTED, {
      transactionId,
      committedAt: new Date(),
    });

    // 向所有参与者发送提交请求
    for (const participantId of Object.keys(transaction.participants)) {
      const participant = transaction.participants[participantId];
      participant.status = ParticipantStatus.COMMITTING;
      participant.lastUpdated = new Date();

      // 如果是本地参与者，直接调用处理器
      const handler = this.participantHandlers.get(participantId);
      if (handler) {
        try {
          const committed = await handler.commit(transactionId);
          if (committed) {
            participant.status = ParticipantStatus.COMMITTED;
            participant.lastUpdated = new Date();
          } else {
            this.logger.warn(`参与者 ${participantId} 提交失败，但事务已进入提交阶段，无法回滚`);
          }
        } catch (error) {
          this.logger.error(`参与者 ${participantId} 提交阶段异常: ${error.message}`, error.stack);
        }
      } else {
        // 远程参与者，通过事件总线发送提交请求
        await this.eventBusService.publish(`${participantId}.commit`, {
          transactionId,
        });
      }
    }

    // 完成事务
    await this.completeTransaction(transactionId);
  }

  /**
   * 中止事务
   * @param transactionId 事务ID
   * @param reason 原因
   */
  private async abortTransaction(transactionId: string, reason: string) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      return {
        success: false,
        error: `事务 ${transactionId} 不存在`,
        transactionId,
      };
    }

    // 更新事务状态
    transaction.status = TransactionStatus.ABORTING;
    await this.logTransaction(transaction);

    // 发布事务中止事件
    await this.eventBusService.publish(TRANSACTION_ABORTED, {
      transactionId,
      reason,
      abortedAt: new Date(),
    });

    // 向所有参与者发送回滚请求
    for (const participantId of Object.keys(transaction.participants)) {
      const participant = transaction.participants[participantId];
      participant.status = ParticipantStatus.ABORTING;
      participant.lastUpdated = new Date();

      // 如果是本地参与者，直接调用处理器
      const handler = this.participantHandlers.get(participantId);
      if (handler) {
        try {
          const rolledBack = await handler.rollback(transactionId);
          if (rolledBack) {
            participant.status = ParticipantStatus.ABORTED;
            participant.lastUpdated = new Date();
          } else {
            this.logger.warn(`参与者 ${participantId} 回滚失败`);
          }
        } catch (error) {
          this.logger.error(`参与者 ${participantId} 回滚阶段异常: ${error.message}`, error.stack);
        }
      } else {
        // 远程参与者，通过事件总线发送回滚请求
        await this.eventBusService.publish(`${participantId}.rollback`, {
          transactionId,
        });
      }
    }

    // 完成事务
    transaction.status = TransactionStatus.ABORTED;
    transaction.endTime = new Date();
    await this.logTransaction(transaction);

    // 发布事务完成事件
    await this.eventBusService.publish(TRANSACTION_COMPLETED, {
      transactionId,
      status: TransactionStatus.ABORTED,
      reason,
      completedAt: transaction.endTime,
    });

    // 从内存中移除事务
    this.transactions.delete(transactionId);
  }

  /**
   * 完成事务
   * @param transactionId 事务ID
   */
  private async completeTransaction(transactionId: string) {
    const transaction = this.transactions.get(transactionId);
    if (!transaction) {
      return;
    }

    // 更新事务状态
    transaction.status = TransactionStatus.COMMITTED;
    transaction.endTime = new Date();
    await this.logTransaction(transaction);

    // 发布事务完成事件
    await this.eventBusService.publish(TRANSACTION_COMPLETED, {
      transactionId,
      status: TransactionStatus.COMMITTED,
      completedAt: transaction.endTime,
    });

    // 从内存中移除事务
    this.transactions.delete(transactionId);
  }

  /**
   * 记录事务日志
   * @param transaction 事务对象
   */
  private async logTransaction(transaction: Transaction) {
    if (!this.options.enableLogging) {
      return;
    }

    try {
      const logEntry = {
        type: 'transaction',
        timestamp: new Date().toISOString(),
        status: transaction.status,
        transaction,
      };

      fs.appendFileSync(this.logFilePath, JSON.stringify(logEntry) + '\n');
    } catch (error) {
      this.logger.error(`记录事务日志失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取事务
   * @param transactionId 事务ID
   */
  getTransaction(transactionId: string): Transaction | null {
    return this.transactions.get(transactionId) || null;
  }

  /**
   * 获取所有事务
   */
  getAllTransactions(): Transaction[] {
    return Array.from(this.transactions.values());
  }
}
