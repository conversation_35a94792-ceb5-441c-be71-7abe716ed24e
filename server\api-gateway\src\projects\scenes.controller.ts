/**
 * 场景控制器
 */
import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ScenesService } from './scenes.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('场景')
@Controller('projects/:projectId/scenes')
export class ScenesController {
  constructor(private readonly scenesService: ScenesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建场景' })
  @ApiResponse({ status: 201, description: '场景创建成功' })
  async create(@Param('projectId') projectId: string, @Request() req, @Body() createSceneDto: any) {
    return this.scenesService.create(projectId, req.user.id, createSceneDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取项目的所有场景' })
  @ApiResponse({ status: 200, description: '返回项目的所有场景' })
  async findAll(@Param('projectId') projectId: string, @Request() req) {
    return this.scenesService.findAll(projectId, req.user.id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取场景' })
  @ApiResponse({ status: 200, description: '返回场景信息' })
  async findOne(@Param('id') id: string, @Request() req) {
    return this.scenesService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新场景' })
  @ApiResponse({ status: 200, description: '场景更新成功' })
  async update(@Param('id') id: string, @Request() req, @Body() updateSceneDto: any) {
    return this.scenesService.update(id, req.user.id, updateSceneDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除场景' })
  @ApiResponse({ status: 204, description: '场景删除成功' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.scenesService.remove(id, req.user.id);
  }

  @Post(':id/entities')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建场景实体' })
  @ApiResponse({ status: 201, description: '场景实体创建成功' })
  async createEntity(@Param('id') id: string, @Request() req, @Body() createEntityDto: any) {
    return this.scenesService.createEntity(id, req.user.id, createEntityDto);
  }

  @Get(':id/entities')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取场景的所有实体' })
  @ApiResponse({ status: 200, description: '返回场景的所有实体' })
  async findAllEntities(@Param('id') id: string, @Request() req) {
    return this.scenesService.findAllEntities(id, req.user.id);
  }

  @Get(':sceneId/entities/:entityId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取场景实体' })
  @ApiResponse({ status: 200, description: '返回场景实体信息' })
  async findOneEntity(@Param('entityId') entityId: string, @Request() req) {
    return this.scenesService.findOneEntity(entityId, req.user.id);
  }

  @Patch(':sceneId/entities/:entityId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新场景实体' })
  @ApiResponse({ status: 200, description: '场景实体更新成功' })
  async updateEntity(
    @Param('entityId') entityId: string,
    @Request() req,
    @Body() updateEntityDto: any,
  ) {
    return this.scenesService.updateEntity(entityId, req.user.id, updateEntityDto);
  }

  @Delete(':sceneId/entities/:entityId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除场景实体' })
  @ApiResponse({ status: 204, description: '场景实体删除成功' })
  async removeEntity(@Param('entityId') entityId: string, @Request() req) {
    return this.scenesService.removeEntity(entityId, req.user.id);
  }
}
