/**
 * API网关主服务
 */
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AppService implements OnModuleInit {
  private readonly logger = new Logger(AppService.name);
  
  constructor(
    private readonly configService: ConfigService,
    @Inject('SERVICE_REGISTRY') private readonly serviceRegistry: ClientProxy,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
    @Inject('ASSET_SERVICE') private readonly assetService: ClientProxy,
    @Inject('RENDER_SERVICE') private readonly renderService: ClientProxy,
  ) {}
  
  async onModuleInit() {
    // 等待所有微服务连接
    try {
      await Promise.all([
        this.serviceRegistry.connect(),
        this.userService.connect(),
        this.projectService.connect(),
        this.assetService.connect(),
        this.renderService.connect(),
      ]);
      this.logger.log('所有微服务连接成功');
    } catch (error) {
      this.logger.error('微服务连接失败', error);
    }
  }
  
  getInfo() {
    return {
      name: 'DL（Digital Learning）引擎API网关',
      version: '1.0.0',
      description: 'DL（Digital Learning）引擎API网关，作为前端和微服务之间的中介',
      environment: this.configService.get<string>('NODE_ENV', 'development'),
    };
  }
  
  async healthCheck() {
    const services = {
      gateway: { status: 'up' },
      serviceRegistry: { status: 'unknown' },
      userService: { status: 'unknown' },
      projectService: { status: 'unknown' },
      assetService: { status: 'unknown' },
      renderService: { status: 'unknown' },
    };
    
    try {
      // 检查服务注册中心
      await firstValueFrom(this.serviceRegistry.ping(), { defaultValue: false });
      services.serviceRegistry.status = 'up';
    } catch (error) {
      services.serviceRegistry.status = 'down';
      this.logger.error('服务注册中心健康检查失败', error);
    }
    
    try {
      // 检查用户服务
      await firstValueFrom(this.userService.ping(), { defaultValue: false });
      services.userService.status = 'up';
    } catch (error) {
      services.userService.status = 'down';
      this.logger.error('用户服务健康检查失败', error);
    }
    
    try {
      // 检查项目服务
      await firstValueFrom(this.projectService.ping(), { defaultValue: false });
      services.projectService.status = 'up';
    } catch (error) {
      services.projectService.status = 'down';
      this.logger.error('项目服务健康检查失败', error);
    }
    
    try {
      // 检查资产服务
      await firstValueFrom(this.assetService.ping(), { defaultValue: false });
      services.assetService.status = 'up';
    } catch (error) {
      services.assetService.status = 'down';
      this.logger.error('资产服务健康检查失败', error);
    }
    
    try {
      // 检查渲染服务
      await firstValueFrom(this.renderService.ping(), { defaultValue: false });
      services.renderService.status = 'up';
    } catch (error) {
      services.renderService.status = 'down';
      this.logger.error('渲染服务健康检查失败', error);
    }
    
    const allUp = Object.values(services).every(service => service.status === 'up');
    
    return {
      status: allUp ? 'up' : 'degraded',
      timestamp: new Date().toISOString(),
      services,
    };
  }
}
