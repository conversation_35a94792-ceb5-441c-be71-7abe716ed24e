/**
 * 场景服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ScenesService {
  private readonly logger = new Logger(ScenesService.name);

  constructor(
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  /**
   * 创建场景
   */
  async create(projectId: string, userId: string, createSceneDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'createScene' },
          { projectId, userId, ...createSceneDto }
        )
      );
    } catch (error) {
      this.logger.error(`创建项目ID ${projectId} 场景失败`, error);
      throw error;
    }
  }

  /**
   * 获取项目的所有场景
   */
  async findAll(projectId: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'findAllScenes' },
          { projectId, userId }
        )
      );
    } catch (error) {
      this.logger.error(`获取项目ID ${projectId} 所有场景失败`, error);
      throw error;
    }
  }

  /**
   * 根据ID获取场景
   */
  async findOne(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'findSceneById' },
          { id, userId }
        )
      );
    } catch (error) {
      this.logger.error(`获取场景ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 更新场景
   */
  async update(id: string, userId: string, updateSceneDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'updateScene' },
          { id, userId, ...updateSceneDto }
        )
      );
    } catch (error) {
      this.logger.error(`更新场景ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除场景
   */
  async remove(id: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'removeScene' },
          { id, userId }
        )
      );
    } catch (error) {
      this.logger.error(`删除场景ID ${id} 失败`, error);
      throw error;
    }
  }

  /**
   * 创建场景实体
   */
  async createEntity(sceneId: string, userId: string, createEntityDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'createSceneEntity' },
          { sceneId, userId, ...createEntityDto }
        )
      );
    } catch (error) {
      this.logger.error(`创建场景ID ${sceneId} 实体失败`, error);
      throw error;
    }
  }

  /**
   * 获取场景的所有实体
   */
  async findAllEntities(sceneId: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'findAllSceneEntities' },
          { sceneId, userId }
        )
      );
    } catch (error) {
      this.logger.error(`获取场景ID ${sceneId} 所有实体失败`, error);
      throw error;
    }
  }

  /**
   * 根据ID获取场景实体
   */
  async findOneEntity(entityId: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'findSceneEntityById' },
          { entityId, userId }
        )
      );
    } catch (error) {
      this.logger.error(`获取场景实体ID ${entityId} 失败`, error);
      throw error;
    }
  }

  /**
   * 更新场景实体
   */
  async updateEntity(entityId: string, userId: string, updateEntityDto: any) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'updateSceneEntity' },
          { entityId, userId, ...updateEntityDto }
        )
      );
    } catch (error) {
      this.logger.error(`更新场景实体ID ${entityId} 失败`, error);
      throw error;
    }
  }

  /**
   * 删除场景实体
   */
  async removeEntity(entityId: string, userId: string) {
    try {
      return await firstValueFrom(
        this.projectService.send(
          { cmd: 'removeSceneEntity' },
          { entityId, userId }
        )
      );
    } catch (error) {
      this.logger.error(`删除场景实体ID ${entityId} 失败`, error);
      throw error;
    }
  }
}
