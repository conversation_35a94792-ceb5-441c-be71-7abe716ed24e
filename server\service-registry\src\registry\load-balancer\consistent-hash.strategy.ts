/**
 * 一致性哈希负载均衡策略
 */
import { Injectable, Logger } from '@nestjs/common';
import { BaseLoadBalancerStrategy } from './base-load-balancer.strategy';
import { LoadBalancerContext, LoadBalancerAlgorithm } from './load-balancer.interface';
import { ServiceInstanceEntity } from '../entities/service-instance.entity';

/**
 * 虚拟节点信息
 */
interface VirtualNode {
  /** 虚拟节点哈希值 */
  hash: number;
  /** 实例ID */
  instanceId: string;
  /** 虚拟节点索引 */
  index: number;
}

/**
 * 一致性哈希配置
 */
interface ConsistentHashConfig {
  /** 虚拟节点数量 */
  virtualNodeCount: number;
  /** 哈希键生成函数 */
  hashKeyFunction?: (context: LoadBalancerContext) => string;
}

/**
 * 一致性哈希负载均衡策略
 */
@Injectable()
export class ConsistentHashLoadBalancerStrategy extends BaseLoadBalancerStrategy {
  private readonly logger = new Logger(ConsistentHashLoadBalancerStrategy.name);
  
  // 哈希环，按哈希值排序
  private readonly hashRings = new Map<string, VirtualNode[]>();
  
  // 实例映射，用于快速查找实例
  private readonly instanceMaps = new Map<string, Map<string, ServiceInstanceEntity>>();
  
  // 默认配置
  private readonly defaultConfig: ConsistentHashConfig = {
    virtualNodeCount: 100,
    hashKeyFunction: (context: LoadBalancerContext) => {
      // 默认使用客户端IP或会话ID作为哈希键
      return context.sessionId || context.clientIp || context.extra?.userId || Math.random().toString();
    },
  };
  
  constructor() {
    super(LoadBalancerAlgorithm.CONSISTENT_HASH);
    this.config.algorithm = LoadBalancerAlgorithm.CONSISTENT_HASH;
    this.config.options = this.defaultConfig;
  }
  
  /**
   * 使用一致性哈希选择服务实例
   * @param instances 服务实例列表
   * @param context 负载均衡上下文
   */
  protected async doSelect(
    instances: ServiceInstanceEntity[],
    context: LoadBalancerContext,
  ): Promise<ServiceInstanceEntity | null> {
    if (!instances || instances.length === 0) {
      return null;
    }
    
    // 获取服务的哈希环
    const serviceKey = context.serviceName;
    let hashRing = this.hashRings.get(serviceKey);
    let instanceMap = this.instanceMaps.get(serviceKey);
    
    // 如果哈希环不存在或实例列表已变化，则重建哈希环
    if (!hashRing || !instanceMap || this.isInstanceListChanged(instanceMap, instances)) {
      const result = this.buildHashRing(serviceKey, instances);
      hashRing = result.hashRing;
      instanceMap = result.instanceMap;
    }
    
    // 如果哈希环为空，则随机选择一个实例
    if (hashRing.length === 0) {
      return instances[Math.floor(Math.random() * instances.length)];
    }
    
    // 获取哈希键
    const hashKeyFunction = this.getHashKeyFunction();
    const hashKey = hashKeyFunction(context);
    
    // 计算哈希值
    const hash = this.hashCode(hashKey);
    
    // 在哈希环上查找第一个大于等于该哈希值的节点
    const node = this.findNode(hashRing, hash);
    
    // 获取对应的实例
    const instance = instanceMap.get(node.instanceId);
    
    if (!instance) {
      // 如果找不到实例（可能已被移除），则重建哈希环并重试
      const result = this.buildHashRing(serviceKey, instances);
      this.hashRings.set(serviceKey, result.hashRing);
      this.instanceMaps.set(serviceKey, result.instanceMap);
      
      // 重新查找
      const newNode = this.findNode(result.hashRing, hash);
      return result.instanceMap.get(newNode.instanceId) || instances[0];
    }
    
    return instance;
  }
  
  /**
   * 构建哈希环
   * @param serviceKey 服务键
   * @param instances 服务实例列表
   */
  private buildHashRing(
    serviceKey: string,
    instances: ServiceInstanceEntity[],
  ): { hashRing: VirtualNode[], instanceMap: Map<string, ServiceInstanceEntity> } {
    const hashRing: VirtualNode[] = [];
    const instanceMap = new Map<string, ServiceInstanceEntity>();
    
    // 获取虚拟节点数量
    const virtualNodeCount = this.getVirtualNodeCount();
    
    // 为每个实例创建虚拟节点
    for (const instance of instances) {
      instanceMap.set(instance.id, instance);
      
      for (let i = 0; i < virtualNodeCount; i++) {
        const virtualNodeKey = `${instance.id}-${i}`;
        const hash = this.hashCode(virtualNodeKey);
        
        hashRing.push({
          hash,
          instanceId: instance.id,
          index: i,
        });
      }
    }
    
    // 按哈希值排序
    hashRing.sort((a, b) => a.hash - b.hash);
    
    // 更新哈希环和实例映射
    this.hashRings.set(serviceKey, hashRing);
    this.instanceMaps.set(serviceKey, instanceMap);
    
    this.logger.debug(`已为服务 ${serviceKey} 构建哈希环，包含 ${instances.length} 个实例，${hashRing.length} 个虚拟节点`);
    
    return { hashRing, instanceMap };
  }
  
  /**
   * 在哈希环上查找节点
   * @param hashRing 哈希环
   * @param hash 哈希值
   */
  private findNode(hashRing: VirtualNode[], hash: number): VirtualNode {
    // 二分查找第一个大于等于该哈希值的节点
    let left = 0;
    let right = hashRing.length - 1;
    
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      
      if (hashRing[mid].hash === hash) {
        return hashRing[mid];
      } else if (hashRing[mid].hash > hash) {
        right = mid - 1;
      } else {
        left = mid + 1;
      }
    }
    
    // 如果没有找到大于等于该哈希值的节点，则返回第一个节点（形成环）
    return hashRing[left % hashRing.length];
  }
  
  /**
   * 检查实例列表是否已变化
   * @param instanceMap 实例映射
   * @param instances 实例列表
   */
  private isInstanceListChanged(
    instanceMap: Map<string, ServiceInstanceEntity>,
    instances: ServiceInstanceEntity[],
  ): boolean {
    // 检查数量是否一致
    if (instanceMap.size !== instances.length) {
      return true;
    }
    
    // 检查每个实例是否存在
    for (const instance of instances) {
      if (!instanceMap.has(instance.id)) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 获取虚拟节点数量
   */
  private getVirtualNodeCount(): number {
    return (this.config.options as ConsistentHashConfig)?.virtualNodeCount || this.defaultConfig.virtualNodeCount;
  }
  
  /**
   * 获取哈希键生成函数
   */
  private getHashKeyFunction(): (context: LoadBalancerContext) => string {
    return (this.config.options as ConsistentHashConfig)?.hashKeyFunction || this.defaultConfig.hashKeyFunction;
  }
  
  /**
   * 重置策略状态
   */
  override reset(): void {
    this.hashRings.clear();
    this.instanceMaps.clear();
  }
}
