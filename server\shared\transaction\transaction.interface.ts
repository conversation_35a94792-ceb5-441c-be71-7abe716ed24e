/**
 * 事务接口定义
 */

/**
 * 事务状态
 */
export enum TransactionStatus {
  STARTED = 'started',
  PREPARING = 'preparing',
  PREPARED = 'prepared',
  COMMITTING = 'committing',
  COMMITTED = 'committed',
  ABORTING = 'aborting',
  ABORTED = 'aborted',
}

/**
 * 事务参与者状态
 */
export enum ParticipantStatus {
  JOINED = 'joined',
  PREPARING = 'preparing',
  PREPARED = 'prepared',
  COMMITTING = 'committing',
  COMMITTED = 'committed',
  ABORTING = 'aborting',
  ABORTED = 'aborted',
}

/**
 * 事务参与者
 */
export interface TransactionParticipant {
  /**
   * 参与者ID（服务名称）
   */
  id: string;

  /**
   * 参与者状态
   */
  status: ParticipantStatus;

  /**
   * 最后更新时间
   */
  lastUpdated: Date;
}

/**
 * 事务
 */
export interface Transaction {
  /**
   * 事务ID
   */
  id: string;

  /**
   * 事务状态
   */
  status: TransactionStatus;

  /**
   * 事务发起者
   */
  initiator: string;

  /**
   * 事务参与者
   */
  participants: Record<string, TransactionParticipant>;

  /**
   * 事务开始时间
   */
  startTime: Date;

  /**
   * 事务超时时间（毫秒）
   */
  timeout: number;

  /**
   * 事务完成时间
   */
  endTime?: Date;

  /**
   * 事务数据
   */
  data?: any;
}

/**
 * 事务操作结果
 */
export interface TransactionResult {
  /**
   * 是否成功
   */
  success: boolean;

  /**
   * 错误信息
   */
  error?: string;

  /**
   * 事务ID
   */
  transactionId: string;
}

/**
 * 事务参与者接口
 */
export interface TransactionParticipantHandler {
  /**
   * 准备阶段
   * @param transactionId 事务ID
   * @param data 事务数据
   */
  prepare(transactionId: string, data?: any): Promise<boolean>;

  /**
   * 提交阶段
   * @param transactionId 事务ID
   */
  commit(transactionId: string): Promise<boolean>;

  /**
   * 回滚阶段
   * @param transactionId 事务ID
   */
  rollback(transactionId: string): Promise<boolean>;
}

/**
 * 事务参与者选项
 */
export interface TransactionParticipantOptions {
  /**
   * 超时时间（毫秒）
   */
  timeout?: number;

  /**
   * 重试次数
   */
  retryCount?: number;

  /**
   * 重试延迟（毫秒）
   */
  retryDelay?: number;

  /**
   * 重试策略
   */
  retryStrategy?: 'fixed' | 'exponential' | 'linear';

  /**
   * 是否必须参与者
   */
  required?: boolean;

  /**
   * 优先级（0-100）
   */
  priority?: number;

  /**
   * 是否异步执行
   */
  async?: boolean;

  /**
   * 是否启用超时自动回滚
   */
  enableTimeoutRollback?: boolean;

  /**
   * 是否启用错误自动回滚
   */
  enableErrorRollback?: boolean;

  /**
   * 自定义选项
   */
  [key: string]: any;
}

/**
 * 事务配置
 */
export interface TransactionOptions {
  /**
   * 服务名称
   */
  serviceName: string;

  /**
   * 事务超时时间（毫秒）
   */
  defaultTimeout?: number;

  /**
   * 是否启用事务日志
   */
  enableLogging?: boolean;

  /**
   * 日志存储路径
   */
  logStoragePath?: string;

  /**
   * 默认重试次数
   */
  defaultRetryCount?: number;

  /**
   * 默认重试延迟（毫秒）
   */
  defaultRetryDelay?: number;

  /**
   * 默认重试策略
   */
  defaultRetryStrategy?: 'fixed' | 'exponential' | 'linear';

  /**
   * 是否启用事务监控
   */
  enableMonitoring?: boolean;

  /**
   * 是否启用性能指标收集
   */
  enablePerformanceMetrics?: boolean;
}
