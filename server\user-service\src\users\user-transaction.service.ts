/**
 * 用户事务服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventBusService } from '../../../shared/event-bus';
import { TransactionParticipant } from '../../../shared/transaction/transaction.decorator';
import { TransactionParticipantHandler } from '../../../shared/transaction/transaction.interface';
import { UserEntity } from './entities/user.entity';

interface UserTransactionData {
  action: 'create' | 'update' | 'delete';
  user: Partial<UserEntity>;
  originalUser?: UserEntity;
}

@Injectable()
@TransactionParticipant('user-service')
export class UserTransactionService implements TransactionParticipantHandler {
  private readonly logger = new Logger(UserTransactionService.name);
  private readonly transactionData = new Map<string, UserTransactionData>();

  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    private readonly eventBusService: EventBusService,
  ) {}

  /**
   * 准备阶段
   * @param transactionId 事务ID
   * @param data 事务数据
   */
  async prepare(transactionId: string, data?: any): Promise<boolean> {
    this.logger.debug(`准备事务 ${transactionId}`);
    
    if (!data || !data.userService) {
      this.logger.warn(`事务 ${transactionId} 没有用户服务相关数据`);
      return false;
    }
    
    const { action, user } = data.userService as UserTransactionData;
    
    try {
      switch (action) {
        case 'create':
          // 验证用户数据
          if (!user.username || !user.email) {
            this.logger.warn(`事务 ${transactionId} 创建用户数据不完整`);
            return false;
          }
          
          // 检查用户名和邮箱是否已存在
          const existingUser = await this.userRepository.findOne({
            where: [
              { username: user.username },
              { email: user.email },
            ],
          });
          
          if (existingUser) {
            this.logger.warn(`事务 ${transactionId} 用户名或邮箱已存在`);
            return false;
          }
          
          // 保存事务数据
          this.transactionData.set(transactionId, { action, user });
          break;
          
        case 'update':
          // 验证用户数据
          if (!user.id) {
            this.logger.warn(`事务 ${transactionId} 更新用户数据不完整`);
            return false;
          }
          
          // 检查用户是否存在
          const userToUpdate = await this.userRepository.findOne({
            where: { id: user.id },
          });
          
          if (!userToUpdate) {
            this.logger.warn(`事务 ${transactionId} 要更新的用户不存在`);
            return false;
          }
          
          // 如果要更新用户名或邮箱，检查是否已存在
          if (user.username && user.username !== userToUpdate.username) {
            const existingUsername = await this.userRepository.findOne({
              where: { username: user.username },
            });
            
            if (existingUsername) {
              this.logger.warn(`事务 ${transactionId} 用户名已存在`);
              return false;
            }
          }
          
          if (user.email && user.email !== userToUpdate.email) {
            const existingEmail = await this.userRepository.findOne({
              where: { email: user.email },
            });
            
            if (existingEmail) {
              this.logger.warn(`事务 ${transactionId} 邮箱已存在`);
              return false;
            }
          }
          
          // 保存事务数据
          this.transactionData.set(transactionId, {
            action,
            user,
            originalUser: userToUpdate,
          });
          break;
          
        case 'delete':
          // 验证用户数据
          if (!user.id) {
            this.logger.warn(`事务 ${transactionId} 删除用户数据不完整`);
            return false;
          }
          
          // 检查用户是否存在
          const userToDelete = await this.userRepository.findOne({
            where: { id: user.id },
          });
          
          if (!userToDelete) {
            this.logger.warn(`事务 ${transactionId} 要删除的用户不存在`);
            return false;
          }
          
          // 保存事务数据
          this.transactionData.set(transactionId, {
            action,
            user,
            originalUser: userToDelete,
          });
          break;
          
        default:
          this.logger.warn(`事务 ${transactionId} 未知操作: ${action}`);
          return false;
      }
      
      return true;
    } catch (error) {
      this.logger.error(`事务 ${transactionId} 准备阶段异常: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 提交阶段
   * @param transactionId 事务ID
   */
  async commit(transactionId: string): Promise<boolean> {
    this.logger.debug(`提交事务 ${transactionId}`);
    
    const data = this.transactionData.get(transactionId);
    if (!data) {
      this.logger.warn(`事务 ${transactionId} 没有相关数据`);
      return false;
    }
    
    try {
      switch (data.action) {
        case 'create':
          // 创建用户
          const createdUser = await this.userRepository.save(data.user);
          
          // 发布用户创建事件
          await this.eventBusService.publish('user.created', {
            userId: createdUser.id,
            username: createdUser.username,
            email: createdUser.email,
            createdAt: createdUser.createdAt,
          });
          break;
          
        case 'update':
          // 更新用户
          const updatedUser = await this.userRepository.save({
            ...data.originalUser,
            ...data.user,
          });
          
          // 发布用户更新事件
          await this.eventBusService.publish('user.updated', {
            userId: updatedUser.id,
            username: updatedUser.username,
            email: updatedUser.email,
            updatedAt: updatedUser.updatedAt,
          });
          break;
          
        case 'delete':
          // 删除用户
          await this.userRepository.remove(data.originalUser);
          
          // 发布用户删除事件
          await this.eventBusService.publish('user.deleted', {
            userId: data.originalUser.id,
            deletedAt: new Date(),
          });
          break;
      }
      
      // 清理事务数据
      this.transactionData.delete(transactionId);
      
      return true;
    } catch (error) {
      this.logger.error(`事务 ${transactionId} 提交阶段异常: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 回滚阶段
   * @param transactionId 事务ID
   */
  async rollback(transactionId: string): Promise<boolean> {
    this.logger.debug(`回滚事务 ${transactionId}`);
    
    // 清理事务数据
    this.transactionData.delete(transactionId);
    
    return true;
  }
}
