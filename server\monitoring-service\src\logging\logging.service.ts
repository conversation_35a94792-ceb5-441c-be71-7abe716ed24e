import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LogEntity, LogLevel } from './entities/log.entity';
import { LogQueryEntity } from './entities/log-query.entity';
import { LogStorageService } from './log-storage.service';
import { ElasticsearchLogService } from './elasticsearch-log.service';
import { LogAnalysisService } from './log-analysis.service';

@Injectable()
export class LoggingService {
  private readonly logger = new Logger(LoggingService.name);

  constructor(
    @InjectRepository(LogEntity)
    private readonly logRepository: Repository<LogEntity>,
    @InjectRepository(LogQueryEntity)
    private readonly queryRepository: Repository<LogQueryEntity>,
    private readonly logStorageService: LogStorageService,
    private readonly elasticsearchLogService: ElasticsearchLogService,
    private readonly logAnalysisService: LogAnalysisService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 存储日志
   */
  async storeLog(logData: Partial<LogEntity>): Promise<LogEntity> {
    try {
      // 创建日志实体
      const log = this.logRepository.create({
        ...logData,
        timestamp: logData.timestamp || new Date(),
      });
      
      // 存储到数据库
      const savedLog = await this.logRepository.save(log);
      
      // 存储到Elasticsearch
      await this.elasticsearchLogService.indexLog(savedLog);
      
      // 触发日志存储事件
      this.eventEmitter.emit('logging.log.stored', savedLog);
      
      // 分析日志
      this.logAnalysisService.analyzeLog(savedLog).catch(error => {
        this.logger.error(`分析日志失败: ${error.message}`, error.stack);
      });
      
      return savedLog;
    } catch (error) {
      this.logger.error(`存储日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量存储日志
   */
  async storeLogs(logsData: Partial<LogEntity>[]): Promise<LogEntity[]> {
    try {
      // 创建日志实体
      const logs = logsData.map(logData => this.logRepository.create({
        ...logData,
        timestamp: logData.timestamp || new Date(),
      }));
      
      // 存储到数据库
      const savedLogs = await this.logRepository.save(logs);
      
      // 存储到Elasticsearch
      await this.elasticsearchLogService.indexLogs(savedLogs);
      
      // 触发日志存储事件
      this.eventEmitter.emit('logging.logs.stored', savedLogs);
      
      // 分析日志
      for (const log of savedLogs) {
        this.logAnalysisService.analyzeLog(log).catch(error => {
          this.logger.error(`分析日志失败: ${error.message}`, error.stack);
        });
      }
      
      return savedLogs;
    } catch (error) {
      this.logger.error(`批量存储日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 查询日志
   */
  async queryLogs(params: {
    query?: string;
    levels?: LogLevel[];
    serviceId?: string;
    serviceType?: string;
    instanceId?: string;
    hostname?: string;
    startTime?: Date;
    endTime?: Date;
    limit?: number;
    offset?: number;
    sortField?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<{ logs: LogEntity[]; total: number }> {
    try {
      // 使用Elasticsearch查询
      if (this.elasticsearchLogService.isEnabled()) {
        return this.elasticsearchLogService.searchLogs(params);
      }
      
      // 使用数据库查询
      const query = this.logRepository.createQueryBuilder('log');
      
      // 应用过滤条件
      if (params.query) {
        query.andWhere('log.message LIKE :query OR log.context LIKE :query', {
          query: `%${params.query}%`,
        });
      }
      
      if (params.levels && params.levels.length > 0) {
        query.andWhere('log.level IN (:...levels)', { levels: params.levels });
      }
      
      if (params.serviceId) {
        query.andWhere('log.serviceId = :serviceId', { serviceId: params.serviceId });
      }
      
      if (params.serviceType) {
        query.andWhere('log.serviceType = :serviceType', { serviceType: params.serviceType });
      }
      
      if (params.instanceId) {
        query.andWhere('log.instanceId = :instanceId', { instanceId: params.instanceId });
      }
      
      if (params.hostname) {
        query.andWhere('log.hostname = :hostname', { hostname: params.hostname });
      }
      
      if (params.startTime) {
        query.andWhere('log.timestamp >= :startTime', { startTime: params.startTime });
      }
      
      if (params.endTime) {
        query.andWhere('log.timestamp <= :endTime', { endTime: params.endTime });
      }
      
      // 应用排序
      const sortField = params.sortField || 'timestamp';
      const sortOrder = params.sortOrder || 'DESC';
      
      query.orderBy(`log.${sortField}`, sortOrder);
      
      // 应用分页
      const limit = params.limit || 100;
      const offset = params.offset || 0;
      
      query.take(limit);
      query.skip(offset);
      
      // 执行查询
      const [logs, total] = await query.getManyAndCount();
      
      return { logs, total };
    } catch (error) {
      this.logger.error(`查询日志失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 保存日志查询
   */
  async saveLogQuery(queryData: Partial<LogQueryEntity>): Promise<LogQueryEntity> {
    try {
      const query = this.queryRepository.create(queryData);
      return this.queryRepository.save(query);
    } catch (error) {
      this.logger.error(`保存日志查询失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取日志查询
   */
  async getLogQueries(params: {
    isFavorite?: boolean;
    createdBy?: string;
  }): Promise<LogQueryEntity[]> {
    try {
      const query = this.queryRepository.createQueryBuilder('query');
      
      if (params.isFavorite !== undefined) {
        query.andWhere('query.isFavorite = :isFavorite', { isFavorite: params.isFavorite });
      }
      
      if (params.createdBy) {
        query.andWhere('query.createdBy = :createdBy', { createdBy: params.createdBy });
      }
      
      query.orderBy('query.updatedAt', 'DESC');
      
      return query.getMany();
    } catch (error) {
      this.logger.error(`获取日志查询失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取日志统计信息
   */
  async getLogStats(params: {
    startTime?: Date;
    endTime?: Date;
    serviceType?: string;
  }): Promise<{
    total: number;
    byLevel: Record<LogLevel, number>;
    byService: Record<string, number>;
    byTime: { timestamp: Date; count: number }[];
  }> {
    try {
      // 使用Elasticsearch获取统计信息
      if (this.elasticsearchLogService.isEnabled()) {
        return this.elasticsearchLogService.getLogStats(params);
      }
      
      // 使用数据库获取统计信息
      const query = this.logRepository.createQueryBuilder('log');
      
      if (params.startTime) {
        query.andWhere('log.timestamp >= :startTime', { startTime: params.startTime });
      }
      
      if (params.endTime) {
        query.andWhere('log.timestamp <= :endTime', { endTime: params.endTime });
      }
      
      if (params.serviceType) {
        query.andWhere('log.serviceType = :serviceType', { serviceType: params.serviceType });
      }
      
      // 获取总数
      const total = await query.getCount();
      
      // 按级别统计
      const byLevelResult = await this.logRepository
        .createQueryBuilder('log')
        .select('log.level, COUNT(*) as count')
        .where(query.getWhereExpressions())
        .groupBy('log.level')
        .getRawMany();
      
      const byLevel: Record<LogLevel, number> = {
        [LogLevel.DEBUG]: 0,
        [LogLevel.INFO]: 0,
        [LogLevel.WARN]: 0,
        [LogLevel.ERROR]: 0,
        [LogLevel.FATAL]: 0,
      };
      
      for (const item of byLevelResult) {
        byLevel[item.level] = parseInt(item.count, 10);
      }
      
      // 按服务统计
      const byServiceResult = await this.logRepository
        .createQueryBuilder('log')
        .select('log.serviceType, COUNT(*) as count')
        .where(query.getWhereExpressions())
        .groupBy('log.serviceType')
        .getRawMany();
      
      const byService: Record<string, number> = {};
      
      for (const item of byServiceResult) {
        byService[item.serviceType || 'unknown'] = parseInt(item.count, 10);
      }
      
      // 按时间统计
      const byTimeResult = await this.logRepository
        .createQueryBuilder('log')
        .select('DATE_FORMAT(log.timestamp, "%Y-%m-%d %H:00:00") as timestamp, COUNT(*) as count')
        .where(query.getWhereExpressions())
        .groupBy('timestamp')
        .orderBy('timestamp', 'ASC')
        .getRawMany();
      
      const byTime = byTimeResult.map(item => ({
        timestamp: new Date(item.timestamp),
        count: parseInt(item.count, 10),
      }));
      
      return { total, byLevel, byService, byTime };
    } catch (error) {
      this.logger.error(`获取日志统计信息失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
