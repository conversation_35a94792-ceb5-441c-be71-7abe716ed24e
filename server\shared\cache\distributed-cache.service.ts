/**
 * 分布式缓存服务
 * 提供高级分布式缓存功能，包括多级缓存、缓存分片、一致性机制等
 */
import { Injectable, Inject, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { CacheConfig, CacheLevel } from './interfaces/cache-config.interface';
import { CacheStats } from './interfaces/cache-stats.interface';
import * as Redis from 'ioredis';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';
import { ConsistentHash } from './utils/consistent-hash';

/**
 * 缓存条目
 */
interface CacheEntry<T> {
  /** 数据 */
  data: T;
  /** 过期时间 */
  expireAt: number;
  /** 最后访问时间 */
  lastAccessed: number;
  /** 访问次数 */
  accessCount: number;
  /** 版本号 */
  version: number;
  /** 热点标记 */
  isHotspot: boolean;
}

/**
 * 分布式缓存配置
 */
export interface DistributedCacheConfig extends CacheConfig {
  /** 是否启用缓存分片 */
  enableSharding?: boolean;
  /** 分片数量 */
  shardCount?: number;
  /** 是否启用缓存一致性 */
  enableConsistency?: boolean;
  /** 是否启用缓存预热 */
  enablePrewarming?: boolean;
  /** 预热键列表 */
  prewarmKeys?: string[];
  /** 是否启用热点数据保护 */
  enableHotspotProtection?: boolean;
  /** 热点阈值（访问次数） */
  hotspotThreshold?: number;
  /** 热点TTL倍数 */
  hotspotTtlMultiplier?: number;
  /** 是否启用自适应TTL */
  enableAdaptiveTtl?: boolean;
  /** 最小TTL（毫秒） */
  minTtl?: number;
  /** 最大TTL（毫秒） */
  maxTtl?: number;
  /** 是否启用版本控制 */
  enableVersioning?: boolean;
  /** 是否启用写入确认 */
  enableWriteConfirmation?: boolean;
  /** 写入确认超时（毫秒） */
  writeConfirmationTimeout?: number;
  /** 是否启用缓存统计 */
  enableStats?: boolean;
  /** 统计采样间隔（毫秒） */
  statsSamplingInterval?: number;
}

/**
 * 缓存操作类型
 */
enum CacheOperationType {
  SET = 'set',
  DELETE = 'delete',
  CLEAR = 'clear',
}

/**
 * 缓存操作消息
 */
interface CacheOperationMessage {
  /** 操作类型 */
  type: CacheOperationType;
  /** 缓存键 */
  key?: string;
  /** 版本号 */
  version?: number;
  /** 节点ID */
  nodeId: string;
  /** 时间戳 */
  timestamp: number;
}

@Injectable()
export class DistributedCacheService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DistributedCacheService.name);
  private readonly memoryCache = new Map<string, CacheEntry<any>>();
  private redisClient: Redis.Redis;
  private redisSubscriber: Redis.Redis;
  private cleanupInterval: NodeJS.Timeout;
  private statsInterval: NodeJS.Timeout;
  private nodeId: string;
  private consistentHash: ConsistentHash;

  // 缓存统计
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    size: 0,
    hitRate: 0,
    avgAccessTime: 0,
    memoryUsage: 0,
    expirations: 0,
    evictions: 0,
    writes: 0,
    deletes: 0,
    clears: 0,
  };

  // 访问时间统计
  private totalAccessTime = 0;
  private accessCount = 0;

  // 默认配置
  private readonly defaultConfig: Required<DistributedCacheConfig> = {
    enabled: true,
    levels: [CacheLevel.MEMORY, CacheLevel.REDIS],
    memoryTtl: 60000, // 1分钟
    redisTtl: 300000, // 5分钟
    maxEntries: 10000,
    enableAdaptiveTtl: true,
    enableStats: true,
    debug: false,
    redis: {
      host: 'localhost',
      port: 6379,
      password: '',
      db: 0,
      keyPrefix: 'cache:',
    },
    enableSharding: true,
    shardCount: 16,
    enableConsistency: true,
    enablePrewarming: false,
    prewarmKeys: [],
    enableHotspotProtection: true,
    hotspotThreshold: 100,
    hotspotTtlMultiplier: 5,
    minTtl: 10000, // 10秒
    maxTtl: 3600000, // 1小时
    enableVersioning: true,
    enableWriteConfirmation: true,
    writeConfirmationTimeout: 5000,
    statsSamplingInterval: 60000,
  };

  // 合并后的配置
  private readonly config: Required<DistributedCacheConfig>;

  constructor(
    @Inject('CACHE_OPTIONS') private readonly options: DistributedCacheConfig,
    private readonly eventEmitter: EventEmitter2,
  ) {
    // 生成节点ID
    this.nodeId = uuidv4();

    // 合并配置
    this.config = {
      ...this.defaultConfig,
      ...options,
      redis: {
        ...this.defaultConfig.redis,
        ...options.redis,
      },
    };

    // 初始化一致性哈希
    if (this.config.enableSharding) {
      this.consistentHash = new ConsistentHash(this.config.shardCount);
    }
  }

  /**
   * 模块初始化
   */
  async onModuleInit() {
    if (!this.config.enabled) {
      this.logger.log('分布式缓存服务已禁用');
      return;
    }

    // 初始化Redis客户端
    if (this.config.levels.includes(CacheLevel.REDIS)) {
      try {
        this.redisClient = new Redis({
          host: this.config.redis.host,
          port: this.config.redis.port,
          password: this.config.redis.password || undefined,
          db: this.config.redis.db,
          keyPrefix: this.config.redis.keyPrefix,
        });

        this.logger.log(`已连接到Redis服务器: ${this.config.redis.host}:${this.config.redis.port}`);

        // 如果启用一致性，初始化订阅客户端
        if (this.config.enableConsistency) {
          this.redisSubscriber = new Redis({
            host: this.config.redis.host,
            port: this.config.redis.port,
            password: this.config.redis.password || undefined,
            db: this.config.redis.db,
          });

          // 订阅缓存操作频道
          await this.redisSubscriber.subscribe('cache:operations');

          // 处理缓存操作消息
          this.redisSubscriber.on('message', (channel, message) => {
            if (channel === 'cache:operations') {
              this.handleCacheOperationMessage(JSON.parse(message));
            }
          });

          this.logger.log('已订阅缓存操作频道');
        }
      } catch (error) {
        this.logger.error(`连接Redis服务器失败: ${error.message}`, error.stack);
      }
    }

    // 设置定期清理
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // 每分钟清理一次

    // 设置统计采样
    if (this.config.enableStats) {
      this.statsInterval = setInterval(() => {
        this.sampleStats();
      }, this.config.statsSamplingInterval);
    }

    // 缓存预热
    if (this.config.enablePrewarming && this.config.prewarmKeys.length > 0) {
      await this.prewarmCache();
    }

    this.logger.log(`分布式缓存服务已初始化，节点ID: ${this.nodeId}`);
  }

  /**
   * 模块销毁
   */
  onModuleDestroy() {
    // 清除定时器
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }

    // 关闭Redis连接
    if (this.redisClient) {
      this.redisClient.disconnect();
    }

    if (this.redisSubscriber) {
      this.redisSubscriber.disconnect();
    }

    this.logger.log('分布式缓存服务已销毁');
  }

  /**
   * 从缓存获取数据
   * @param key 缓存键
   * @param fetchFn 获取数据的函数
   * @param ttl 过期时间（毫秒）
   */
  async get<T>(key: string, fetchFn: () => Promise<T>, ttl?: number): Promise<T> {
    if (!this.config.enabled) {
      return fetchFn();
    }

    const cacheKey = this.getCacheKey(key);
    const startTime = Date.now();

    // 尝试从内存缓存获取
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      const memoryEntry = this.memoryCache.get(cacheKey);

      if (memoryEntry && memoryEntry.expireAt > Date.now()) {
        // 更新访问统计
        memoryEntry.lastAccessed = Date.now();
        memoryEntry.accessCount++;

        // 检查是否为热点数据
        if (this.config.enableHotspotProtection && 
            !memoryEntry.isHotspot && 
            memoryEntry.accessCount > this.config.hotspotThreshold) {
          memoryEntry.isHotspot = true;
          
          // 延长热点数据的过期时间
          const originalTtl = memoryEntry.expireAt - memoryEntry.lastAccessed;
          memoryEntry.expireAt = memoryEntry.lastAccessed + (originalTtl * this.config.hotspotTtlMultiplier);
          
          this.logger.debug(`检测到热点数据: ${key}, 访问次数: ${memoryEntry.accessCount}, 延长过期时间`);
        }

        // 更新缓存统计
        this.updateStats('hit', startTime);

        return memoryEntry.data;
      }
    }

    // 尝试从Redis缓存获取
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        const redisData = await this.redisClient.get(redisKey);

        if (redisData) {
          const { data, version } = JSON.parse(redisData);

          // 更新内存缓存
          if (this.config.levels.includes(CacheLevel.MEMORY)) {
            this.setMemoryCache(cacheKey, data, ttl || this.config.memoryTtl, version);
          }

          // 更新缓存统计
          this.updateStats('hit', startTime);

          return data;
        }
      } catch (error) {
        this.logger.warn(`从Redis获取缓存失败: ${error.message}`);
      }
    }

    // 缓存未命中，获取新数据
    this.updateStats('miss', startTime);

    try {
      const data = await fetchFn();

      // 更新缓存
      await this.set(key, data, ttl);

      return data;
    } catch (error) {
      this.logger.error(`获取数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 数据
   * @param ttl 过期时间（毫秒）
   */
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const cacheKey = this.getCacheKey(key);
    const version = this.config.enableVersioning ? Date.now() : 0;

    // 设置内存缓存
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      this.setMemoryCache(cacheKey, data, ttl || this.config.memoryTtl, version);
    }

    // 设置Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        const redisTtl = ttl || this.config.redisTtl;
        const value = JSON.stringify({ data, version });

        await this.redisClient.set(
          redisKey,
          value,
          'PX',
          redisTtl,
        );

        // 发布缓存操作消息
        if (this.config.enableConsistency) {
          await this.publishCacheOperation({
            type: CacheOperationType.SET,
            key,
            version,
            nodeId: this.nodeId,
            timestamp: Date.now(),
          });
        }
      } catch (error) {
        this.logger.warn(`设置Redis缓存失败: ${error.message}`);
      }
    }

    // 更新统计
    this.stats.writes++;
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  async delete(key: string): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const cacheKey = this.getCacheKey(key);

    // 删除内存缓存
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      this.memoryCache.delete(cacheKey);
    }

    // 删除Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const redisKey = this.getRedisKey(key);
        await this.redisClient.del(redisKey);

        // 发布缓存操作消息
        if (this.config.enableConsistency) {
          await this.publishCacheOperation({
            type: CacheOperationType.DELETE,
            key,
            nodeId: this.nodeId,
            timestamp: Date.now(),
          });
        }
      } catch (error) {
        this.logger.warn(`删除Redis缓存失败: ${error.message}`);
      }
    }

    // 更新统计
    this.stats.deletes++;
  }

  /**
   * 清空缓存
   */
  async clear(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    // 清空内存缓存
    if (this.config.levels.includes(CacheLevel.MEMORY)) {
      this.memoryCache.clear();
    }

    // 清空Redis缓存
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        const keys = await this.redisClient.keys(`${this.config.redis.keyPrefix}*`);
        if (keys.length > 0) {
          await this.redisClient.del(...keys);
        }

        // 发布缓存操作消息
        if (this.config.enableConsistency) {
          await this.publishCacheOperation({
            type: CacheOperationType.CLEAR,
            nodeId: this.nodeId,
            timestamp: Date.now(),
          });
        }
      } catch (error) {
        this.logger.warn(`清空Redis缓存失败: ${error.message}`);
      }
    }

    // 更新统计
    this.stats.clears++;
  }

  /**
   * 获取缓存统计
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 设置内存缓存
   * @param key 缓存键
   * @param data 数据
   * @param ttl 过期时间（毫秒）
   * @param version 版本号
   */
  private setMemoryCache<T>(key: string, data: T, ttl: number, version: number = 0): void {
    // 如果启用自适应TTL，根据访问频率调整TTL
    if (this.config.enableAdaptiveTtl) {
      const existingEntry = this.memoryCache.get(key);
      if (existingEntry) {
        // 根据访问频率调整TTL
        const accessRate = existingEntry.accessCount / Math.max(1, (Date.now() - existingEntry.lastAccessed) / 1000);
        const adjustedTtl = Math.min(
          this.config.maxTtl,
          Math.max(this.config.minTtl, ttl * (1 + accessRate))
        );
        ttl = adjustedTtl;
      }
    }

    // 检查缓存大小，如果达到最大容量，清理最近最少使用的条目
    if (this.memoryCache.size >= this.config.maxEntries) {
      this.evictLeastRecentlyUsed();
    }

    // 设置缓存条目
    this.memoryCache.set(key, {
      data,
      expireAt: Date.now() + ttl,
      lastAccessed: Date.now(),
      accessCount: 0,
      version,
      isHotspot: false,
    });
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now();
    let expiredCount = 0;

    // 清理内存缓存
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.expireAt <= now) {
        this.memoryCache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0 && this.config.debug) {
      this.logger.debug(`已清理 ${expiredCount} 个过期缓存条目`);
    }

    // 更新统计
    this.stats.expirations += expiredCount;
    this.stats.size = this.memoryCache.size;
  }

  /**
   * 淘汰最近最少使用的缓存条目
   */
  private evictLeastRecentlyUsed(): void {
    let lruKey: string | null = null;
    let lruTime = Infinity;

    // 查找最近最少使用的条目
    for (const [key, entry] of this.memoryCache.entries()) {
      // 跳过热点数据
      if (this.config.enableHotspotProtection && entry.isHotspot) {
        continue;
      }

      if (entry.lastAccessed < lruTime) {
        lruKey = key;
        lruTime = entry.lastAccessed;
      }
    }

    // 如果找到LRU条目，删除它
    if (lruKey) {
      this.memoryCache.delete(lruKey);
      this.stats.evictions++;

      if (this.config.debug) {
        this.logger.debug(`已淘汰LRU缓存条目: ${lruKey}`);
      }
    }
  }

  /**
   * 更新缓存统计
   * @param type 操作类型
   * @param startTime 开始时间
   */
  private updateStats(type: 'hit' | 'miss', startTime: number): void {
    if (!this.config.enableStats) {
      return;
    }

    const accessTime = Date.now() - startTime;

    if (type === 'hit') {
      this.stats.hits++;
    } else {
      this.stats.misses++;
    }

    this.totalAccessTime += accessTime;
    this.accessCount++;

    // 更新命中率
    this.stats.hitRate = this.stats.hits / (this.stats.hits + this.stats.misses);

    // 更新平均访问时间
    this.stats.avgAccessTime = this.totalAccessTime / this.accessCount;

    // 更新缓存大小
    this.stats.size = this.memoryCache.size;

    // 估算内存使用量
    this.stats.memoryUsage = this.estimateMemoryUsage();
  }

  /**
   * 估算内存使用量
   */
  private estimateMemoryUsage(): number {
    // 简单估算，每个缓存条目约占用1KB
    return this.memoryCache.size * 1024;
  }

  /**
   * 采样统计信息
   */
  private sampleStats(): void {
    if (!this.config.enableStats) {
      return;
    }

    // 发出统计事件
    this.eventEmitter.emit('cache.stats', { ...this.stats });

    if (this.config.debug) {
      this.logger.debug(`缓存统计: 命中率=${this.stats.hitRate.toFixed(2)}, 大小=${this.stats.size}, 平均访问时间=${this.stats.avgAccessTime.toFixed(2)}ms`);
    }
  }

  /**
   * 获取缓存键
   * @param key 原始键
   */
  private getCacheKey(key: string): string {
    // 如果启用分片，使用一致性哈希选择分片
    if (this.config.enableSharding && this.consistentHash) {
      const shardId = this.consistentHash.getNode(key);
      return `shard:${shardId}:${key}`;
    }

    return key;
  }

  /**
   * 获取Redis键
   * @param key 原始键
   */
  private getRedisKey(key: string): string {
    return key;
  }

  /**
   * 发布缓存操作消息
   * @param message 消息
   */
  private async publishCacheOperation(message: CacheOperationMessage): Promise<void> {
    if (!this.config.enableConsistency || !this.redisClient) {
      return;
    }

    try {
      await this.redisClient.publish('cache:operations', JSON.stringify(message));
    } catch (error) {
      this.logger.warn(`发布缓存操作消息失败: ${error.message}`);
    }
  }

  /**
   * 处理缓存操作消息
   * @param message 消息
   */
  private handleCacheOperationMessage(message: CacheOperationMessage): void {
    // 忽略自己发出的消息
    if (message.nodeId === this.nodeId) {
      return;
    }

    switch (message.type) {
      case CacheOperationType.SET:
        // 如果启用版本控制，检查版本
        if (this.config.enableVersioning && message.key && message.version) {
          const cacheKey = this.getCacheKey(message.key);
          const entry = this.memoryCache.get(cacheKey);

          // 只有当收到的版本更新时才更新本地缓存
          if (!entry || entry.version < message.version) {
            // 不删除本地缓存，而是等待下次访问时从Redis获取最新数据
          }
        }
        break;

      case CacheOperationType.DELETE:
        if (message.key) {
          const cacheKey = this.getCacheKey(message.key);
          this.memoryCache.delete(cacheKey);
        }
        break;

      case CacheOperationType.CLEAR:
        this.memoryCache.clear();
        break;
    }
  }

  /**
   * 缓存预热
   */
  private async prewarmCache(): Promise<void> {
    if (!this.config.enablePrewarming || this.config.prewarmKeys.length === 0) {
      return;
    }

    this.logger.log(`开始缓存预热，预热键数量: ${this.config.prewarmKeys.length}`);

    // 从Redis加载预热键
    if (this.config.levels.includes(CacheLevel.REDIS) && this.redisClient) {
      try {
        for (const key of this.config.prewarmKeys) {
          const redisKey = this.getRedisKey(key);
          const redisData = await this.redisClient.get(redisKey);

          if (redisData) {
            const { data, version } = JSON.parse(redisData);
            const cacheKey = this.getCacheKey(key);

            // 更新内存缓存
            if (this.config.levels.includes(CacheLevel.MEMORY)) {
              this.setMemoryCache(cacheKey, data, this.config.memoryTtl, version);
            }
          }
        }

        this.logger.log('缓存预热完成');
      } catch (error) {
        this.logger.error(`缓存预热失败: ${error.message}`, error.stack);
      }
    }
  }
}
